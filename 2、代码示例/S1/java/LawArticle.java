package com.envlaw.datapre;

import java.util.Date;

/**
 * 法律条文实体类
 */
public class LawArticle {
    
    private Long id;
    private String lawName;
    private String articleNumber;
    private String articleContent;
    private String status;
    private Date updatedTime;
    
    // Constructors
    public LawArticle() {}
    
    public LawArticle(String lawName, String articleNumber, String articleContent) {
        this.lawName = lawName;
        this.articleNumber = articleNumber;
        this.articleContent = articleContent;
        this.status = "ACTIVE";
        this.updatedTime = new Date();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getLawName() {
        return lawName;
    }
    
    public void setLawName(String lawName) {
        this.lawName = lawName;
    }
    
    public String getArticleNumber() {
        return articleNumber;
    }
    
    public void setArticleNumber(String articleNumber) {
        this.articleNumber = articleNumber;
    }
    
    public String getArticleContent() {
        return articleContent;
    }
    
    public void setArticleContent(String articleContent) {
        this.articleContent = articleContent;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Date getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }
} 