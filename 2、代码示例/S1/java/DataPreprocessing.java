package com.envlaw.datapre;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.core.env.Environment;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.PostConstruct;
import java.util.stream.Collectors;

/**
 * 数据预处理与评估（Java版）
 * 生态环境行政处罚案卷评查系统
 * 
 * 真实业务场景：
 * - 从数据库加载生态环境领域知识、法律条文、裁量基准、司法判例
 * - 从配置文件读取数据源配置
 * - 从外部API服务获取最新数据
 * - 支持数据缓存和降级方案
 * - 支持批量数据处理和异步处理
 */
@Service
public class DataPreprocessing {
    
    private static final Logger logger = LoggerFactory.getLogger(DataPreprocessing.class);
    
    @Autowired
    private Environment environment;
    
    @Autowired
    private DataSourceService dataSourceService;
    
    @Autowired
    private ExternalApiService externalApiService;
    
    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private EnvironmentKnowledgeRepository environmentKnowledgeRepository;
    
    @Autowired
    private LawArticleRepository lawArticleRepository;
    
    @Autowired
    private DiscretionStandardRepository discretionStandardRepository;
    
    @Autowired
    private LegalCaseRepository legalCaseRepository;
    
    // 数据缓存
    private final Map<String, Object> dataCache = new ConcurrentHashMap<>();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 配置参数 - 从配置文件读取
    private String environmentKnowledgeApiUrl;
    private String lawArticlesApiUrl;
    private String discretionStandardsApiUrl;
    private int cacheExpirationHours;
    private int maxRetryAttempts;
    private int maxBatchSize;
    private String dataExportPath;
    
    /**
     * 初始化配置参数
     */
    @PostConstruct
    public void init() {
        this.environmentKnowledgeApiUrl = environment.getProperty("environment.knowledge.api.url");
        this.lawArticlesApiUrl = environment.getProperty("law.articles.api.url");
        this.discretionStandardsApiUrl = environment.getProperty("discretion.standards.api.url");
        this.cacheExpirationHours = Integer.parseInt(environment.getProperty("cache.expiration.hours", "24"));
        this.maxRetryAttempts = Integer.parseInt(environment.getProperty("max.retry.attempts", "3"));
        this.maxBatchSize = Integer.parseInt(environment.getProperty("batch.size.max", "1000"));
        this.dataExportPath = environment.getProperty("data.export.path", "/data/exports/");
        
        logger.info("数据预处理服务初始化完成，配置：cacheExpirationHours={}, maxRetryAttempts={}, maxBatchSize={}", 
                   cacheExpirationHours, maxRetryAttempts, maxBatchSize);
    }
    
    /**
     * 从数据库加载生态环境领域知识
     */
    public Map<String, List<String>> loadEnvironmentKnowledgeFromDatabase() {
        String cacheKey = "environment_knowledge";
        
        // 检查缓存
        if (dataCache.containsKey(cacheKey)) {
            logger.info("从缓存加载生态环境领域知识");
            return (Map<String, List<String>>) dataCache.get(cacheKey);
        }
        
        Map<String, List<String>> knowledge = new HashMap<>();
        
        try {
            // 使用Repository层进行数据访问
            List<EnvironmentKnowledge> knowledgeList = environmentKnowledgeRepository.findActiveKnowledge();
            
            for (EnvironmentKnowledge envKnowledge : knowledgeList) {
                String category = envKnowledge.getCategory();
                String term = envKnowledge.getTerm();
                knowledge.computeIfAbsent(category, k -> new ArrayList<>()).add(term);
            }
            
            logger.info("从数据库加载了 {} 个生态环境领域知识类别，共 {} 个术语", 
                       knowledge.size(), knowledge.values().stream().mapToInt(List::size).sum());
            
            // 缓存数据
            dataCache.put(cacheKey, knowledge);
            
        } catch (Exception e) {
            logger.error("从数据库加载生态环境领域知识失败", e);
            throw new RuntimeException("加载生态环境领域知识失败", e);
        }
        
        return knowledge;
    }
    
    /**
     * 从外部API服务加载生态环境领域知识
     */
    public Map<String, List<String>> loadEnvironmentKnowledgeFromApi() {
        try {
            if (environmentKnowledgeApiUrl == null) {
                logger.warn("生态环境领域知识API地址未配置，使用数据库数据");
                return loadEnvironmentKnowledgeFromDatabase();
            }
            
            String response = externalApiService.get(environmentKnowledgeApiUrl, maxRetryAttempts);
            Map<String, List<String>> knowledge = objectMapper.readValue(response, 
                new TypeReference<Map<String, List<String>>>() {});
            
            logger.info("从外部API加载了 {} 个生态环境领域知识类别", knowledge.size());
            
            // 更新缓存
            dataCache.put("environment_knowledge", knowledge);
            
            return knowledge;
            
        } catch (Exception e) {
            logger.error("从外部API加载生态环境领域知识失败，降级到数据库", e);
            return loadEnvironmentKnowledgeFromDatabase();
        }
    }
    
    /**
     * 从数据库加载法律条文
     */
    public Map<String, List<String>> loadLawArticlesFromDatabase() {
        String cacheKey = "law_articles";
        
        // 检查缓存
        if (dataCache.containsKey(cacheKey)) {
            logger.info("从缓存加载法律条文");
            return (Map<String, List<String>>) dataCache.get(cacheKey);
        }
        
        Map<String, List<String>> articles = new HashMap<>();
        
        try {
            // 使用Repository层进行数据访问
            List<LawArticle> lawArticles = lawArticleRepository.findActiveArticles();
            
            for (LawArticle article : lawArticles) {
                String lawName = article.getLawName();
                String articleNumber = article.getArticleNumber();
                String articleContent = article.getArticleContent();
                
                String fullArticle = articleNumber + " " + articleContent;
                articles.computeIfAbsent(lawName, k -> new ArrayList<>()).add(fullArticle);
            }
            
            logger.info("从数据库加载了 {} 个法律，共 {} 个条文", 
                       articles.size(), articles.values().stream().mapToInt(List::size).sum());
            
            // 缓存数据
            dataCache.put(cacheKey, articles);
            
        } catch (Exception e) {
            logger.error("从数据库加载法律条文失败", e);
            throw new RuntimeException("加载法律条文失败", e);
        }
        
        return articles;
    }
    
    /**
     * 从外部API服务加载法律条文
     */
    public Map<String, List<String>> loadLawArticlesFromApi() {
        try {
            if (lawArticlesApiUrl == null) {
                logger.warn("法律条文API地址未配置，使用数据库数据");
                return loadLawArticlesFromDatabase();
            }
            
            String response = externalApiService.get(lawArticlesApiUrl, maxRetryAttempts);
            Map<String, List<String>> articles = objectMapper.readValue(response, 
                new TypeReference<Map<String, List<String>>>() {});
            
            logger.info("从外部API加载了 {} 个法律", articles.size());
            
            // 更新缓存
            dataCache.put("law_articles", articles);
            
            return articles;
            
        } catch (Exception e) {
            logger.error("从外部API加载法律条文失败，降级到数据库", e);
            return loadLawArticlesFromDatabase();
        }
    }
    
    /**
     * 从数据库加载裁量基准
     */
    public Map<String, List<String>> loadDiscretionStandardsFromDatabase() {
        String cacheKey = "discretion_standards";
        
        // 检查缓存
        if (dataCache.containsKey(cacheKey)) {
            logger.info("从缓存加载裁量基准");
            return (Map<String, List<String>>) dataCache.get(cacheKey);
        }
        
        Map<String, List<String>> standards = new HashMap<>();
        
        try {
            // 使用Repository层进行数据访问
            List<DiscretionStandard> discretionStandards = discretionStandardRepository.findActiveStandards();
            
            for (DiscretionStandard standard : discretionStandards) {
                String standardType = standard.getStandardType();
                String standardContent = standard.getStandardContent();
                
                standards.computeIfAbsent(standardType, k -> new ArrayList<>()).add(standardContent);
            }
            
            logger.info("从数据库加载了 {} 个裁量基准类型，共 {} 个标准", 
                       standards.size(), standards.values().stream().mapToInt(List::size).sum());
            
            // 缓存数据
            dataCache.put(cacheKey, standards);
            
        } catch (Exception e) {
            logger.error("从数据库加载裁量基准失败", e);
            throw new RuntimeException("加载裁量基准失败", e);
        }
        
        return standards;
    }
    
    /**
     * 从外部API服务加载裁量基准
     */
    public Map<String, List<String>> loadDiscretionStandardsFromApi() {
        try {
            if (discretionStandardsApiUrl == null) {
                logger.warn("裁量基准API地址未配置，使用数据库数据");
                return loadDiscretionStandardsFromDatabase();
            }
            
            String response = externalApiService.get(discretionStandardsApiUrl, maxRetryAttempts);
            Map<String, List<String>> standards = objectMapper.readValue(response, 
                new TypeReference<Map<String, List<String>>>() {});
            
            logger.info("从外部API加载了 {} 个裁量基准类型", standards.size());
            
            // 更新缓存
            dataCache.put("discretion_standards", standards);
            
            return standards;
            
        } catch (Exception e) {
            logger.error("从外部API加载裁量基准失败，降级到数据库", e);
            return loadDiscretionStandardsFromDatabase();
        }
    }
    
    /**
     * 从数据库加载司法判例
     */
    public List<LegalCase> loadLegalCasesFromDatabase() {
        String cacheKey = "legal_cases";
        
        // 检查缓存
        if (dataCache.containsKey(cacheKey)) {
            logger.info("从缓存加载司法判例");
            return (List<LegalCase>) dataCache.get(cacheKey);
        }
        
        List<LegalCase> cases = new ArrayList<>();
        
        try {
            // 使用Repository层进行数据访问
            List<LegalCaseEntity> legalCaseEntities = legalCaseRepository.findValidCases(maxBatchSize);
            
            for (LegalCaseEntity entity : legalCaseEntities) {
                LegalCase legalCase = new LegalCase();
                legalCase.setCaseId(entity.getCaseId());
                legalCase.setCaseType(entity.getCaseType());
                legalCase.setViolationType(entity.getViolationType());
                legalCase.setPenaltyAmount(entity.getPenaltyAmount());
                legalCase.setLegalBasis(entity.getLegalBasis());
                legalCase.setCaseDate(entity.getCaseDate());
                
                // 解析程序违规问题（JSON格式存储）
                String proceduralIssuesJson = entity.getProceduralIssues();
                if (proceduralIssuesJson != null) {
                    try {
                        List<String> issues = objectMapper.readValue(proceduralIssuesJson, 
                            new TypeReference<List<String>>() {});
                        legalCase.setProceduralIssues(issues);
                    } catch (IOException e) {
                        logger.warn("解析程序违规问题失败：{}", proceduralIssuesJson);
                        legalCase.setProceduralIssues(new ArrayList<>());
                    }
                } else {
                    legalCase.setProceduralIssues(new ArrayList<>());
                }
                
                cases.add(legalCase);
            }
            
            logger.info("从数据库加载了 {} 个司法判例", cases.size());
            
            // 缓存数据
            dataCache.put(cacheKey, cases);
            
        } catch (Exception e) {
            logger.error("从数据库加载司法判例失败", e);
            throw new RuntimeException("加载司法判例失败", e);
        }
        
        return cases;
    }
    
    /**
     * 生成训练数据样本
     */
    public List<TrainingSample> generateTrainingSamples() {
        List<TrainingSample> samples = new ArrayList<>();
        
        try {
            // 加载生态环境领域知识
            Map<String, List<String>> envKnowledge = loadEnvironmentKnowledgeFromApi();
            for (Map.Entry<String, List<String>> entry : envKnowledge.entrySet()) {
                for (String term : entry.getValue()) {
                    TrainingSample sample = new TrainingSample();
                    sample.setInput("什么是" + entry.getKey() + "中的" + term + "？");
                    sample.setOutput(term + "是" + entry.getKey() + "的一个重要组成部分，在生态环境执法中具有重要意义。");
                    sample.setType("domain_knowledge");
                    sample.setCategory(entry.getKey());
                    sample.setCreatedTime(new Date());
                    samples.add(sample);
                }
            }
            
            // 加载法律条文
            Map<String, List<String>> lawArticles = loadLawArticlesFromApi();
            for (Map.Entry<String, List<String>> entry : lawArticles.entrySet()) {
                for (String article : entry.getValue()) {
                    TrainingSample sample = new TrainingSample();
                    sample.setInput("请解释" + entry.getKey() + "中的这条规定：" + article.substring(0, Math.min(50, article.length())) + "...");
                    sample.setOutput("这是" + entry.getKey() + "中关于环境违法行为处罚的规定，明确了违法行为的认定标准和处罚措施。");
                    sample.setType("law_understanding");
                    sample.setCategory(entry.getKey());
                    sample.setCreatedTime(new Date());
                    samples.add(sample);
                }
            }
            
            // 加载裁量基准
            Map<String, List<String>> discretionStandards = loadDiscretionStandardsFromApi();
            for (Map.Entry<String, List<String>> entry : discretionStandards.entrySet()) {
                for (String standard : entry.getValue()) {
                    TrainingSample sample = new TrainingSample();
                    sample.setInput("在什么情况下适用" + entry.getKey() + "：" + standard + "？");
                    sample.setOutput("当违法行为符合" + standard + "的条件时，可以适用" + entry.getKey() + "进行处罚。具体适用条件需要根据案件的具体情况来判断。");
                    sample.setType("discretion_application");
                    sample.setCategory(entry.getKey());
                    sample.setCreatedTime(new Date());
                    samples.add(sample);
                }
            }
            
            // 加载司法判例
            List<LegalCase> legalCases = loadLegalCasesFromDatabase();
            for (LegalCase legalCase : legalCases) {
                TrainingSample sample = new TrainingSample();
                sample.setInput("分析案例" + legalCase.getCaseId() + "中的违法行为类型和处罚依据。");
                sample.setOutput("该案例属于" + legalCase.getCaseType() + "，违法行为为" + legalCase.getViolationType() + 
                               "，处罚依据为" + legalCase.getLegalBasis() + "，处罚金额为" + legalCase.getPenaltyAmount() + "元。");
                sample.setType("case_analysis");
                sample.setCategory(legalCase.getCaseType());
                sample.setCreatedTime(new Date());
                samples.add(sample);
            }
            
            logger.info("生成了 {} 个训练样本", samples.size());
            
        } catch (Exception e) {
            logger.error("生成训练样本失败", e);
            throw new RuntimeException("生成训练样本失败", e);
        }
        
        return samples;
    }
    
    /**
     * 保存训练样本到数据库
     */
    public void saveTrainingSamplesToDatabase(List<TrainingSample> samples) {
        try {
            int batchSize = Integer.parseInt(environment.getProperty("batch.size", "100"));
            int count = 0;
            
            for (int i = 0; i < samples.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, samples.size());
                List<TrainingSample> batch = samples.subList(i, endIndex);
                
                // 使用Repository层进行批量保存
                trainingSampleRepository.saveBatch(batch);
                count += batch.size();
                
                logger.info("已保存 {} 个训练样本", count);
            }
            
            logger.info("成功保存 {} 个训练样本到数据库", samples.size());
            
        } catch (Exception e) {
            logger.error("保存训练样本到数据库失败", e);
            throw new RuntimeException("保存训练样本失败", e);
        }
    }
    
    /**
     * 保存训练样本到JSON文件
     */
    public void saveTrainingSamplesToJson(List<TrainingSample> samples, String filePath) throws IOException {
        try {
            // 确保目录存在
            File file = new File(filePath);
            file.getParentFile().mkdirs();
            
            objectMapper.writeValue(file, samples);
            logger.info("训练样本已保存到：{}", filePath);
        } catch (IOException e) {
            logger.error("保存训练样本到JSON文件失败", e);
            throw e;
        }
    }
    
    /**
     * 评估训练样本质量
     */
    public EvaluationResult evaluateTrainingSamples(List<TrainingSample> samples) {
        EvaluationResult result = new EvaluationResult();
        
        try {
            // 统计各类型样本数量
            Map<String, Long> typeCounts = samples.stream()
                .collect(Collectors.groupingBy(TrainingSample::getType, Collectors.counting()));
            
            // 统计各类别样本数量
            Map<String, Long> categoryCounts = samples.stream()
                .collect(Collectors.groupingBy(TrainingSample::getCategory, Collectors.counting()));
            
            // 检查样本质量
            int validSamples = 0;
            int invalidSamples = 0;
            
            for (TrainingSample sample : samples) {
                if (isValidSample(sample)) {
                    validSamples++;
                } else {
                    invalidSamples++;
                }
            }
            
            result.setTotalSamples(samples.size());
            result.setValidSamples(validSamples);
            result.setInvalidSamples(invalidSamples);
            result.setTypeCounts(typeCounts);
            result.setCategoryCounts(categoryCounts);
            result.setSuccess(true);
            
            logger.info("评估完成，总样本数：{}，有效样本：{}，无效样本：{}", 
                       samples.size(), validSamples, invalidSamples);
            
        } catch (Exception e) {
            logger.error("评估训练样本失败", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查样本是否有效
     */
    private boolean isValidSample(TrainingSample sample) {
        return sample.getInput() != null && !sample.getInput().trim().isEmpty() &&
               sample.getOutput() != null && !sample.getOutput().trim().isEmpty() &&
               sample.getType() != null && !sample.getType().trim().isEmpty();
    }
    
    /**
     * 清理缓存
     */
    public void clearCache() {
        dataCache.clear();
        logger.info("数据缓存已清理");
    }
    
    // 内部类
    public static class TrainingSample {
        private String input;
        private String output;
        private String type;
        private String category;
        private Date createdTime;
        
        // getters and setters
        public String getInput() { return input; }
        public void setInput(String input) { this.input = input; }
        public String getOutput() { return output; }
        public void setOutput(String output) { this.output = output; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public Date getCreatedTime() { return createdTime; }
        public void setCreatedTime(Date createdTime) { this.createdTime = createdTime; }
    }
    
    public static class LegalCase {
        private String caseId;
        private String caseType;
        private String violationType;
        private int penaltyAmount;
        private String legalBasis;
        private List<String> proceduralIssues;
        private Date caseDate;
        
        // getters and setters
        public String getCaseId() { return caseId; }
        public void setCaseId(String caseId) { this.caseId = caseId; }
        public String getCaseType() { return caseType; }
        public void setCaseType(String caseType) { this.caseType = caseType; }
        public String getViolationType() { return violationType; }
        public void setViolationType(String violationType) { this.violationType = violationType; }
        public int getPenaltyAmount() { return penaltyAmount; }
        public void setPenaltyAmount(int penaltyAmount) { this.penaltyAmount = penaltyAmount; }
        public String getLegalBasis() { return legalBasis; }
        public void setLegalBasis(String legalBasis) { this.legalBasis = legalBasis; }
        public List<String> getProceduralIssues() { return proceduralIssues; }
        public void setProceduralIssues(List<String> proceduralIssues) { this.proceduralIssues = proceduralIssues; }
        public Date getCaseDate() { return caseDate; }
        public void setCaseDate(Date caseDate) { this.caseDate = caseDate; }
    }
    
    public static class EvaluationResult {
        private boolean success;
        private int totalSamples;
        private int validSamples;
        private int invalidSamples;
        private Map<String, Long> typeCounts;
        private Map<String, Long> categoryCounts;
        private String errorMessage;
        
        // getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public int getTotalSamples() { return totalSamples; }
        public void setTotalSamples(int totalSamples) { this.totalSamples = totalSamples; }
        public int getValidSamples() { return validSamples; }
        public void setValidSamples(int validSamples) { this.validSamples = validSamples; }
        public int getInvalidSamples() { return invalidSamples; }
        public void setInvalidSamples(int invalidSamples) { this.invalidSamples = invalidSamples; }
        public Map<String, Long> getTypeCounts() { return typeCounts; }
        public void setTypeCounts(Map<String, Long> typeCounts) { this.typeCounts = typeCounts; }
        public Map<String, Long> getCategoryCounts() { return categoryCounts; }
        public void setCategoryCounts(Map<String, Long> categoryCounts) { this.categoryCounts = categoryCounts; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}
