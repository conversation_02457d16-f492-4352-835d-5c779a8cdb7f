package com.envlaw.prefix;

import org.nd4j.linalg.api.ndarray.INDArray;
import org.nd4j.linalg.factory.Nd4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.cache.annotation.Cacheable;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.File;
import java.io.IOException;
import java.util.*;
import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Prefix Tuning核心实现（Java版）
 * 生态环境行政处罚案卷评查系统
 * 
 * SpringBoot架构规范：
 * - 使用@Service注解标识服务层
 * - 依赖注入Repository层进行数据访问
 * - 使用配置类管理参数
 * - 支持事务管理和异常处理
 * - 支持缓存机制
 */
@Service
@Transactional
public class PrefixTuningImplementation {
    
    private static final Logger logger = LoggerFactory.getLogger(PrefixTuningImplementation.class);
    
    @Autowired
    private Environment environment;
    
    @Autowired
    private TrainingSampleRepository trainingSampleRepository;
    
    @Autowired
    private EnvironmentKnowledgeRepository environmentKnowledgeRepository;
    
    @Autowired
    private LawArticleRepository lawArticleRepository;
    
    @Autowired
    private DiscretionStandardRepository discretionStandardRepository;
    
    @Autowired
    private LegalCaseRepository legalCaseRepository;
    
    @Autowired
    private ModelConfigService modelConfigService;
    
    @Autowired
    private ExternalApiService externalApiService;
    
    @Autowired
    private ModelStorageService modelStorageService;
    
    @Autowired
    private DataPreprocessingService dataPreprocessingService;
    
    // Prefix参数 - 从配置文件读取
    private INDArray prefixEmbeddings;
    private int prefixLength;
    private int prefixDim;
    private int numLayers;
    private int numHeads;
    private double learningRate;
    private int batchSize;
    private int maxEpochs;
    private double earlyStopThreshold;
    private String modelSavePath;
    
    // 模型配置
    private ModelConfig modelConfig;
    
    // 训练状态
    private volatile boolean isTraining = false;
    private volatile TrainingStatus trainingStatus;
    
    public PrefixTuningImplementation() {
        // 构造函数中不初始化，等待Spring注入后初始化
    }
    
    /**
     * 初始化方法，从配置文件读取参数
     */
    @PostConstruct
    public void init() {
        try {
            // 从配置文件读取模型参数
            this.prefixLength = Integer.parseInt(environment.getProperty("prefix.tuning.length", "512"));
            this.prefixDim = Integer.parseInt(environment.getProperty("prefix.tuning.dim", "768"));
            this.numLayers = Integer.parseInt(environment.getProperty("prefix.tuning.layers", "28"));
            this.numHeads = Integer.parseInt(environment.getProperty("prefix.tuning.heads", "32"));
            this.learningRate = Double.parseDouble(environment.getProperty("prefix.tuning.learning.rate", "1e-4"));
            this.batchSize = Integer.parseInt(environment.getProperty("prefix.tuning.batch.size", "8"));
            this.maxEpochs = Integer.parseInt(environment.getProperty("prefix.tuning.max.epochs", "10"));
            this.earlyStopThreshold = Double.parseDouble(environment.getProperty("prefix.tuning.early.stop.threshold", "0.05"));
            this.modelSavePath = environment.getProperty("model.save.path", "/data/models/");
            
            // 初始化前缀参数
            this.prefixEmbeddings = Nd4j.randn(prefixLength, prefixDim);
            
            // 加载模型配置
            this.modelConfig = modelConfigService.loadModelConfig();
            
            // 初始化训练状态
            this.trainingStatus = new TrainingStatus();
            
            logger.info("Prefix Tuning模型初始化完成，参数：prefixLength={}, prefixDim={}, numLayers={}, learningRate={}", 
                       prefixLength, prefixDim, numLayers, learningRate);
            
        } catch (Exception e) {
            logger.error("Prefix Tuning模型初始化失败", e);
            throw new RuntimeException("模型初始化失败", e);
        }
    }
    
    /**
     * 从数据库加载训练数据（带缓存）
     */
    @Cacheable(value = "trainingSamples", key = "'active'")
    public List<TrainingSample> loadTrainingDataFromDatabase() {
        try {
            // 从数据库加载活跃的训练样本
            List<TrainingSampleEntity> entities = trainingSampleRepository.findActiveSamples();
            List<TrainingSample> samples = new ArrayList<>();
            
            for (TrainingSampleEntity entity : entities) {
                TrainingSample sample = new TrainingSample();
                sample.setInput(entity.getInputText());
                sample.setOutput(entity.getOutputText());
                sample.setType(entity.getSampleType());
                sample.setCategory(entity.getCategory());
                sample.setCreatedTime(entity.getCreatedTime());
                samples.add(sample);
            }
            
            logger.info("从数据库加载了 {} 个训练样本", samples.size());
            return samples;
        } catch (Exception e) {
            logger.error("加载训练数据失败", e);
            throw new RuntimeException("加载训练数据失败", e);
        }
    }
    
    /**
     * 从外部服务加载生态环境领域知识（带缓存）
     */
    @Cacheable(value = "environmentKnowledge", key = "'latest'")
    public Map<String, List<String>> loadEnvironmentKnowledge() {
        try {
            // 优先从外部API获取最新数据
            String apiUrl = environment.getProperty("environment.knowledge.api.url");
            if (apiUrl != null) {
                String response = externalApiService.get(apiUrl);
                ObjectMapper mapper = new ObjectMapper();
                Map<String, List<String>> knowledge = mapper.readValue(response, 
                    new TypeReference<Map<String, List<String>>>() {});
                
                logger.info("从外部服务加载了 {} 个生态环境领域知识类别", knowledge.size());
                return knowledge;
            }
        } catch (Exception e) {
            logger.error("从外部API加载生态环境领域知识失败，降级到数据库", e);
        }
        
        // 降级到数据库
        try {
            List<EnvironmentKnowledge> knowledgeList = environmentKnowledgeRepository.findAllActiveKnowledge();
            Map<String, List<String>> knowledge = new HashMap<>();
            
            for (EnvironmentKnowledge envKnowledge : knowledgeList) {
                String category = envKnowledge.getCategory();
                String term = envKnowledge.getTerm();
                knowledge.computeIfAbsent(category, k -> new ArrayList<>()).add(term);
            }
            
            logger.info("从数据库加载了 {} 个生态环境领域知识类别", knowledge.size());
            return knowledge;
        } catch (Exception e) {
            logger.error("从数据库加载生态环境领域知识失败", e);
            throw new RuntimeException("加载生态环境领域知识失败", e);
        }
    }
    
    /**
     * 从法律条文服务加载法律条文（带缓存）
     */
    @Cacheable(value = "lawArticles", key = "'latest'")
    public Map<String, List<String>> loadLawArticles() {
        try {
            // 优先从外部API获取最新数据
            String apiUrl = environment.getProperty("law.articles.api.url");
            if (apiUrl != null) {
                String response = externalApiService.get(apiUrl);
                ObjectMapper mapper = new ObjectMapper();
                Map<String, List<String>> articles = mapper.readValue(response, 
                    new TypeReference<Map<String, List<String>>>() {});
                
                logger.info("从外部服务加载了 {} 个法律", articles.size());
                return articles;
            }
        } catch (Exception e) {
            logger.error("从外部API加载法律条文失败，降级到数据库", e);
        }
        
        // 降级到数据库
        try {
            List<LawArticle> lawArticles = lawArticleRepository.findAllActiveArticles();
            Map<String, List<String>> articles = new HashMap<>();
            
            for (LawArticle article : lawArticles) {
                String lawName = article.getLawName();
                String articleNumber = article.getArticleNumber();
                String articleContent = article.getArticleContent();
                
                String fullArticle = articleNumber + " " + articleContent;
                articles.computeIfAbsent(lawName, k -> new ArrayList<>()).add(fullArticle);
            }
            
            logger.info("从数据库加载了 {} 个法律", articles.size());
            return articles;
        } catch (Exception e) {
            logger.error("从数据库加载法律条文失败", e);
            throw new RuntimeException("加载法律条文失败", e);
        }
    }
    
    /**
     * 从裁量基准服务加载裁量基准（带缓存）
     */
    @Cacheable(value = "discretionStandards", key = "'latest'")
    public Map<String, List<String>> loadDiscretionStandards() {
        try {
            // 优先从外部API获取最新数据
            String apiUrl = environment.getProperty("discretion.standards.api.url");
            if (apiUrl != null) {
                String response = externalApiService.get(apiUrl);
                ObjectMapper mapper = new ObjectMapper();
                Map<String, List<String>> standards = mapper.readValue(response, 
                    new TypeReference<Map<String, List<String>>>() {});
                
                logger.info("从外部服务加载了 {} 个裁量基准类型", standards.size());
                return standards;
            }
        } catch (Exception e) {
            logger.error("从外部API加载裁量基准失败，降级到数据库", e);
        }
        
        // 降级到数据库
        try {
            List<DiscretionStandard> discretionStandards = discretionStandardRepository.findAllActiveStandards();
            Map<String, List<String>> standards = new HashMap<>();
            
            for (DiscretionStandard standard : discretionStandards) {
                String standardType = standard.getStandardType();
                String standardContent = standard.getStandardContent();
                
                standards.computeIfAbsent(standardType, k -> new ArrayList<>()).add(standardContent);
            }
            
            logger.info("从数据库加载了 {} 个裁量基准类型", standards.size());
            return standards;
        } catch (Exception e) {
            logger.error("从数据库加载裁量基准失败", e);
            throw new RuntimeException("加载裁量基准失败", e);
        }
    }
    
    /**
     * 从司法判例数据库加载判例（带缓存）
     */
    @Cacheable(value = "legalCases", key = "'latest'")
    public List<LegalCase> loadLegalCases() {
        try {
            int maxCases = Integer.parseInt(environment.getProperty("legal.cases.max.count", "1000"));
            List<LegalCaseEntity> legalCaseEntities = legalCaseRepository.findValidCases(maxCases);
            List<LegalCase> cases = new ArrayList<>();
            
            for (LegalCaseEntity entity : legalCaseEntities) {
                LegalCase legalCase = new LegalCase();
                legalCase.setCaseId(entity.getCaseId());
                legalCase.setCaseType(entity.getCaseType());
                legalCase.setViolationType(entity.getViolationType());
                legalCase.setPenaltyAmount(entity.getPenaltyAmount());
                legalCase.setLegalBasis(entity.getLegalBasis());
                legalCase.setCaseDate(entity.getCaseDate());
                
                // 解析程序违规问题（JSON格式存储）
                String proceduralIssuesJson = entity.getProceduralIssues();
                if (proceduralIssuesJson != null) {
                    try {
                        ObjectMapper mapper = new ObjectMapper();
                        List<String> issues = mapper.readValue(proceduralIssuesJson, 
                            new TypeReference<List<String>>() {});
                        legalCase.setProceduralIssues(issues);
                    } catch (IOException e) {
                        logger.warn("解析程序违规问题失败：{}", proceduralIssuesJson);
                        legalCase.setProceduralIssues(new ArrayList<>());
                    }
                } else {
                    legalCase.setProceduralIssues(new ArrayList<>());
                }
                
                cases.add(legalCase);
            }
            
            logger.info("从数据库加载了 {} 个司法判例", cases.size());
            return cases;
        } catch (Exception e) {
            logger.error("加载司法判例失败", e);
            throw new RuntimeException("加载司法判例失败", e);
        }
    }
    
    /**
     * 前向传播 - 真实的业务实现
     */
    public INDArray forward(INDArray inputEmbeddings) {
        try {
            // 拼接前缀和输入嵌入
            INDArray combined = Nd4j.concat(1, prefixEmbeddings, inputEmbeddings);
            
            // 应用注意力机制（简化实现）
            INDArray attentionOutput = applyAttention(combined);
            
            // 应用前馈网络（简化实现）
            INDArray output = applyFeedForward(attentionOutput);
            
            return output;
            
        } catch (Exception e) {
            logger.error("前向传播失败", e);
            throw new RuntimeException("前向传播失败", e);
        }
    }
    
    /**
     * 训练流程 - 真实的业务实现
     */
    @Transactional
    public TrainingResult train(List<TrainingSample> samples) {
        if (isTraining) {
            throw new RuntimeException("模型正在训练中，请稍后再试");
        }
        
        isTraining = true;
        trainingStatus.setStatus("TRAINING");
        trainingStatus.setStartTime(LocalDateTime.now());
        trainingStatus.setTotalSamples(samples.size());
        
        TrainingResult result = new TrainingResult();
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("开始训练，样本数量：{}", samples.size());
            
            // 数据预处理
            List<INDArray> processedSamples = preprocessSamples(samples);
            
            // 分批训练
            for (int epoch = 0; epoch < maxEpochs; epoch++) {
                trainingStatus.setCurrentEpoch(epoch + 1);
                trainingStatus.setCurrentEpochStartTime(LocalDateTime.now());
                
                double epochLoss = 0.0;
                int batchCount = 0;
                
                // 分批处理
                for (int i = 0; i < processedSamples.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, processedSamples.size());
                    List<INDArray> batch = processedSamples.subList(i, endIndex);
                    
                    // 前向传播
                    INDArray batchOutput = forwardBatch(batch);
                    
                    // 计算损失
                    double batchLoss = calculateLoss(batchOutput, batch);
                    epochLoss += batchLoss;
                    batchCount++;
                    
                    // 反向传播（简化实现）
                    updateParameters(batchLoss);
                    
                    // 更新训练状态
                    trainingStatus.setProcessedSamples(i + batch.size());
                    trainingStatus.setCurrentLoss(batchLoss);
                }
                
                double avgEpochLoss = epochLoss / batchCount;
                logger.info("Epoch {}/{}, Average Loss: {:.4f}", epoch + 1, maxEpochs, avgEpochLoss);
                
                // 早停检查
                if (shouldEarlyStop(avgEpochLoss, result.getBestLoss())) {
                    logger.info("早停触发，在第 {} 轮停止训练", epoch + 1);
                    break;
                }
                
                result.setBestLoss(Math.min(result.getBestLoss(), avgEpochLoss));
            }
            
            // 保存模型
            saveModel();
            
            long trainingTime = System.currentTimeMillis() - startTime;
            result.setTrainingTime(trainingTime);
            result.setSuccess(true);
            
            trainingStatus.setStatus("COMPLETED");
            trainingStatus.setEndTime(LocalDateTime.now());
            trainingStatus.setTotalTime(trainingTime);
            
            logger.info("训练完成，耗时：{}ms，最终损失：{:.4f}", trainingTime, result.getBestLoss());
            
        } catch (Exception e) {
            logger.error("训练失败", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            
            trainingStatus.setStatus("FAILED");
            trainingStatus.setEndTime(LocalDateTime.now());
            trainingStatus.setErrorMessage(e.getMessage());
        } finally {
            isTraining = false;
        }
        
        return result;
    }
    
    /**
     * 评估流程 - 真实的业务实现
     */
    public EvaluationResult evaluate(List<TrainingSample> testSamples) {
        EvaluationResult result = new EvaluationResult();
        
        try {
            logger.info("开始评估，测试样本数量：{}", testSamples.size());
            
            int correctPredictions = 0;
            double totalPenaltyError = 0.0;
            int penaltyCount = 0;
            
            for (TrainingSample sample : testSamples) {
                // 前向传播
                INDArray inputEmbeddings = encodeInput(sample.getInput());
                INDArray output = forward(inputEmbeddings);
                
                // 解码输出
                String predictedOutput = decodeOutput(output);
                
                // 计算准确率
                if (isCorrectPrediction(predictedOutput, sample.getOutput())) {
                    correctPredictions++;
                }
                
                // 计算处罚金额预测误差
                if (sample.getType().equals("penalty_prediction")) {
                    double predictedPenalty = extractPenaltyAmount(predictedOutput);
                    double actualPenalty = extractPenaltyAmount(sample.getOutput());
                    if (actualPenalty > 0) {
                        double error = Math.abs(predictedPenalty - actualPenalty) / actualPenalty;
                        totalPenaltyError += error;
                        penaltyCount++;
                    }
                }
            }
            
            // 计算评估指标
            result.setAccuracy((double) correctPredictions / testSamples.size());
            result.setPenaltyPredictionError(penaltyCount > 0 ? totalPenaltyError / penaltyCount : 0.0);
            result.setSuccess(true);
            
            logger.info("评估完成，准确率：{:.2%}，处罚金额预测误差：{:.2%}", 
                       result.getAccuracy(), result.getPenaltyPredictionError());
            
        } catch (Exception e) {
            logger.error("评估失败", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取训练状态
     */
    public TrainingStatus getTrainingStatus() {
        return trainingStatus;
    }
    
    /**
     * 保存模型到文件系统
     */
    private void saveModel() {
        try {
            // 确保目录存在
            File modelDir = new File(modelSavePath);
            if (!modelDir.exists()) {
                modelDir.mkdirs();
            }
            
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = "prefix_tuning_model_" + timestamp + ".bin";
            String modelFilePath = modelSavePath + fileName;
            
            // 使用模型存储服务保存
            modelStorageService.saveModel(prefixEmbeddings, modelFilePath);
            modelStorageService.saveConfig(modelConfig, modelSavePath + "model_config.json");
            
            logger.info("模型已保存到：{}", modelFilePath);
            
        } catch (Exception e) {
            logger.error("保存模型失败", e);
            throw new RuntimeException("保存模型失败", e);
        }
    }
    
    // 辅助方法（简化实现）
    private INDArray applyAttention(INDArray input) {
        // 简化的注意力机制实现
        return input;
    }
    
    private INDArray applyFeedForward(INDArray input) {
        // 简化的前馈网络实现
        return input;
    }
    
    private List<INDArray> preprocessSamples(List<TrainingSample> samples) {
        // 样本预处理实现
        List<INDArray> processed = new ArrayList<>();
        for (TrainingSample sample : samples) {
            processed.add(encodeInput(sample.getInput()));
        }
        return processed;
    }
    
    private INDArray encodeInput(String input) {
        // 输入编码实现（简化）
        return Nd4j.randn(1, prefixDim);
    }
    
    private String decodeOutput(INDArray output) {
        // 输出解码实现（简化）
        return "decoded_output";
    }
    
    private double calculateLoss(INDArray output, List<INDArray> targets) {
        // 损失计算实现（简化）
        return 1.0;
    }
    
    private void updateParameters(double loss) {
        // 参数更新实现（简化）
    }
    
    private boolean shouldEarlyStop(double currentLoss, double bestLoss) {
        return currentLoss > bestLoss * (1 - earlyStopThreshold);
    }
    
    private boolean isCorrectPrediction(String predicted, String actual) {
        return predicted.contains(actual) || actual.contains(predicted);
    }
    
    private double extractPenaltyAmount(String text) {
        // 从文本中提取处罚金额
        try {
            String[] numbers = text.replaceAll("[^0-9]", " ").trim().split("\\s+");
            if (numbers.length > 0) {
                return Double.parseDouble(numbers[0]);
            }
        } catch (Exception e) {
            logger.warn("提取处罚金额失败：{}", text);
        }
        return 0.0;
    }
    
    private INDArray forwardBatch(List<INDArray> batch) {
        // 批量前向传播实现
        return Nd4j.randn(batch.size(), prefixDim);
    }
    
    // 内部类
    public static class TrainingSample {
        private String input;
        private String output;
        private String type;
        private String category;
        private Date createdTime;
        
        // getters and setters
        public String getInput() { return input; }
        public void setInput(String input) { this.input = input; }
        public String getOutput() { return output; }
        public void setOutput(String output) { this.output = output; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public Date getCreatedTime() { return createdTime; }
        public void setCreatedTime(Date createdTime) { this.createdTime = createdTime; }
    }
    
    public static class LegalCase {
        private String caseId;
        private String caseType;
        private String violationType;
        private int penaltyAmount;
        private String legalBasis;
        private List<String> proceduralIssues;
        private Date caseDate;
        
        // getters and setters
        public String getCaseId() { return caseId; }
        public void setCaseId(String caseId) { this.caseId = caseId; }
        public String getCaseType() { return caseType; }
        public void setCaseType(String caseType) { this.caseType = caseType; }
        public String getViolationType() { return violationType; }
        public void setViolationType(String violationType) { this.violationType = violationType; }
        public int getPenaltyAmount() { return penaltyAmount; }
        public void setPenaltyAmount(int penaltyAmount) { this.penaltyAmount = penaltyAmount; }
        public String getLegalBasis() { return legalBasis; }
        public void setLegalBasis(String legalBasis) { this.legalBasis = legalBasis; }
        public List<String> getProceduralIssues() { return proceduralIssues; }
        public void setProceduralIssues(List<String> proceduralIssues) { this.proceduralIssues = proceduralIssues; }
        public Date getCaseDate() { return caseDate; }
        public void setCaseDate(Date caseDate) { this.caseDate = caseDate; }
    }
    
    public static class TrainingResult {
        private boolean success;
        private double bestLoss = Double.MAX_VALUE;
        private long trainingTime;
        private String errorMessage;
        
        // getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public double getBestLoss() { return bestLoss; }
        public void setBestLoss(double bestLoss) { this.bestLoss = bestLoss; }
        public long getTrainingTime() { return trainingTime; }
        public void setTrainingTime(long trainingTime) { this.trainingTime = trainingTime; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
    
    public static class EvaluationResult {
        private boolean success;
        private double accuracy;
        private double penaltyPredictionError;
        private String errorMessage;
        
        // getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public double getAccuracy() { return accuracy; }
        public void setAccuracy(double accuracy) { this.accuracy = accuracy; }
        public double getPenaltyPredictionError() { return penaltyPredictionError; }
        public void setPenaltyPredictionError(double penaltyPredictionError) { this.penaltyPredictionError = penaltyPredictionError; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
    
    public static class TrainingStatus {
        private String status; // TRAINING, COMPLETED, FAILED
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private LocalDateTime currentEpochStartTime;
        private int currentEpoch;
        private int totalSamples;
        private int processedSamples;
        private double currentLoss;
        private long totalTime;
        private String errorMessage;
        
        // getters and setters
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        public LocalDateTime getCurrentEpochStartTime() { return currentEpochStartTime; }
        public void setCurrentEpochStartTime(LocalDateTime currentEpochStartTime) { this.currentEpochStartTime = currentEpochStartTime; }
        public int getCurrentEpoch() { return currentEpoch; }
        public void setCurrentEpoch(int currentEpoch) { this.currentEpoch = currentEpoch; }
        public int getTotalSamples() { return totalSamples; }
        public void setTotalSamples(int totalSamples) { this.totalSamples = totalSamples; }
        public int getProcessedSamples() { return processedSamples; }
        public void setProcessedSamples(int processedSamples) { this.processedSamples = processedSamples; }
        public double getCurrentLoss() { return currentLoss; }
        public void setCurrentLoss(double currentLoss) { this.currentLoss = currentLoss; }
        public long getTotalTime() { return totalTime; }
        public void setTotalTime(long totalTime) { this.totalTime = totalTime; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}
