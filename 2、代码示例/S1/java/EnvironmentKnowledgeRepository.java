package com.envlaw.datapre;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 生态环境领域知识Repository接口
 */
@Repository
public interface EnvironmentKnowledgeRepository extends JpaRepository<EnvironmentKnowledge, Long> {
    
    /**
     * 查找所有活跃的生态环境领域知识
     */
    @Query("SELECT ek FROM EnvironmentKnowledge ek WHERE ek.status = 'ACTIVE' ORDER BY ek.category, ek.term")
    List<EnvironmentKnowledge> findActiveKnowledge();
    
    /**
     * 根据类别查找生态环境领域知识
     */
    @Query("SELECT ek FROM EnvironmentKnowledge ek WHERE ek.category = ?1 AND ek.status = 'ACTIVE'")
    List<EnvironmentKnowledge> findByCategory(String category);
} 