# 生态环境行政处罚案卷评查系统 - 模型配置（开发层次）

## 1. 模型配置文件结构

### 1.1 model-config.json（模型参数配置）

```json
{
  "model_config": {
    "model_name": "chatglm3-6b",
    "model_type": "transformer",
    "base_model_path": "/models/chatglm3-6b",
    "vocab_size": 65024,
    "hidden_size": 4096,
    "num_attention_heads": 32,
    "num_hidden_layers": 28,
    "intermediate_size": 13696,
    "max_position_embeddings": 2048,
    "layer_norm_eps": 1e-5,
    "dropout": 0.1,
    "attention_dropout": 0.1,
    "activation_function": "gelu",
    "pad_token_id": 0,
    "bos_token_id": 1,
    "eos_token_id": 2,
    "tie_word_embeddings": false,
    "torch_dtype": "float16",
    "transformers_version": "4.35.0",
    "use_cache": true,
    "architectures": ["ChatGLMModel"]
  },
  "tokenizer_config": {
    "tokenizer_type": "ChatGLMTokenizer",
    "vocab_file": "/models/chatglm3-6b/tokenizer.model",
    "model_max_length": 2048,
    "padding_side": "left",
    "truncation_side": "right",
    "clean_up_tokenization_spaces": true,
    "use_fast": false
  },
  "preprocessing_config": {
    "max_length": 1024,
    "truncation": true,
    "padding": "max_length",
    "return_tensors": "pt",
    "add_special_tokens": true
  }
}
```

### 1.2 training-config.json（训练参数配置）

```json
{
  "training_config": {
    "model_name": "chatglm3-6b",
    "training_type": "multi_task_learning",
    "base_config": {
      "learning_rate": 2e-5,
      "batch_size": 8,
      "max_epochs": 10,
      "warmup_steps": 100,
      "weight_decay": 0.01,
      "gradient_accumulation_steps": 4,
      "max_grad_norm": 1.0,
      "lr_scheduler_type": "cosine",
      "save_steps": 500,
      "eval_steps": 500,
      "logging_steps": 100,
      "save_total_limit": 3,
      "load_best_model_at_end": true,
      "metric_for_best_model": "eval_loss",
      "greater_is_better": false
    },
    "multi_task_config": {
      "tasks": [
        {
          "name": "article_classification",
          "weight": 0.4,
          "target_loss": 0.05,
          "metrics": ["accuracy", "precision", "recall", "f1"]
        },
        {
          "name": "defect_detection",
          "weight": 0.3,
          "target_loss": 0.1,
          "metrics": ["accuracy", "precision", "recall", "f1"]
        },
        {
          "name": "penalty_prediction",
          "weight": 0.2,
          "target_loss": 0.15,
          "metrics": ["mse", "mae", "r2"]
        },
        {
          "name": "procedure_classification",
          "weight": 0.1,
          "target_loss": 0.1,
          "metrics": ["accuracy", "precision", "recall", "f1"]
        }
      ],
      "loss_aggregation": "weighted_sum",
      "early_stopping_patience": 5,
      "min_improvement": 0.05
    },
    "data_config": {
      "train_file": "/data/training_samples.json",
      "validation_file": "/data/validation_samples.json",
      "test_file": "/data/test_samples.json",
      "text_column": "text",
      "label_columns": {
        "article_label": "article_classification",
        "defect_label": "defect_detection",
        "penalty_amount": "penalty_prediction",
        "procedure_type": "procedure_classification"
      },
      "data_processing": {
        "text_cleaning": true,
        "normalization": true,
        "augmentation": false
      }
    },
    "hardware_config": {
      "device": "cuda",
      "num_gpus": 1,
      "mixed_precision": "fp16",
      "gradient_checkpointing": true,
      "dataloader_num_workers": 4
    }
  }
}
```

### 1.3 evaluation-config.json（评估参数配置）

```json
{
  "evaluation_config": {
    "model_name": "chatglm3-6b",
    "evaluation_type": "comprehensive",
    "base_config": {
      "batch_size": 16,
      "max_length": 1024,
      "num_beams": 1,
      "do_sample": false,
      "temperature": 1.0,
      "top_p": 1.0,
      "top_k": 50,
      "repetition_penalty": 1.0
    },
    "metrics_config": {
      "classification_metrics": {
        "accuracy": true,
        "precision": true,
        "recall": true,
        "f1_score": true,
        "confusion_matrix": true,
        "classification_report": true
      },
      "regression_metrics": {
        "mse": true,
        "mae": true,
        "r2_score": true,
        "explained_variance": true
      },
      "custom_metrics": {
        "business_accuracy": true,
        "legal_compliance_score": true
      }
    },
    "evaluation_datasets": [
      {
        "name": "test_set",
        "file_path": "/data/test_samples.json",
        "description": "标准测试集"
      },
      {
        "name": "validation_set",
        "file_path": "/data/validation_samples.json",
        "description": "验证集"
      },
      {
        "name": "edge_cases",
        "file_path": "/data/edge_cases.json",
        "description": "边界情况测试集"
      }
    ],
    "output_config": {
      "save_predictions": true,
      "save_metrics": true,
      "generate_report": true,
      "output_dir": "/results/evaluation"
    }
  }
}
```

## 2. 配置管理类

### 2.1 ModelConfigManager.java

```java
package com.ecoenvironment.review.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;
import java.io.File;
import java.io.IOException;
import java.util.Map;

/**
 * 模型配置管理器
 */
@Component
public class ModelConfigManager {
    
    private final ObjectMapper objectMapper;
    private Map<String, Object> modelConfig;
    private Map<String, Object> trainingConfig;
    private Map<String, Object> evaluationConfig;
    
    public ModelConfigManager() {
        this.objectMapper = new ObjectMapper();
        loadConfigs();
    }
    
    /**
     * 加载所有配置文件
     */
    private void loadConfigs() {
        try {
            this.modelConfig = objectMapper.readValue(
                new File("config/model-config.json"), Map.class);
            this.trainingConfig = objectMapper.readValue(
                new File("config/training-config.json"), Map.class);
            this.evaluationConfig = objectMapper.readValue(
                new File("config/evaluation-config.json"), Map.class);
        } catch (IOException e) {
            throw new RuntimeException("Failed to load config files", e);
        }
    }
    
    /**
     * 获取模型配置
     */
    public Map<String, Object> getModelConfig() {
        return modelConfig;
    }
    
    /**
     * 获取训练配置
     */
    public Map<String, Object> getTrainingConfig() {
        return trainingConfig;
    }
    
    /**
     * 获取评估配置
     */
    public Map<String, Object> getEvaluationConfig() {
        return evaluationConfig;
    }
    
    /**
     * 更新配置
     */
    public void updateConfig(String configType, Map<String, Object> newConfig) {
        try {
            switch (configType) {
                case "model":
                    objectMapper.writeValue(new File("config/model-config.json"), newConfig);
                    this.modelConfig = newConfig;
                    break;
                case "training":
                    objectMapper.writeValue(new File("config/training-config.json"), newConfig);
                    this.trainingConfig = newConfig;
                    break;
                case "evaluation":
                    objectMapper.writeValue(new File("config/evaluation-config.json"), newConfig);
                    this.evaluationConfig = newConfig;
                    break;
                default:
                    throw new IllegalArgumentException("Unknown config type: " + configType);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to update config", e);
        }
    }
}
```

### 2.2 TrainingConfig.java

```java
package com.ecoenvironment.review.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;

/**
 * 训练配置类
 */
@Component
@ConfigurationProperties(prefix = "training")
public class TrainingConfig {
    
    private String modelName;
    private String trainingType;
    private BaseConfig baseConfig;
    private MultiTaskConfig multiTaskConfig;
    private DataConfig dataConfig;
    private HardwareConfig hardwareConfig;
    
    // Getters and Setters
    public String getModelName() { return modelName; }
    public void setModelName(String modelName) { this.modelName = modelName; }
    
    public String getTrainingType() { return trainingType; }
    public void setTrainingType(String trainingType) { this.trainingType = trainingType; }
    
    public BaseConfig getBaseConfig() { return baseConfig; }
    public void setBaseConfig(BaseConfig baseConfig) { this.baseConfig = baseConfig; }
    
    public MultiTaskConfig getMultiTaskConfig() { return multiTaskConfig; }
    public void setMultiTaskConfig(MultiTaskConfig multiTaskConfig) { this.multiTaskConfig = multiTaskConfig; }
    
    public DataConfig getDataConfig() { return dataConfig; }
    public void setDataConfig(DataConfig dataConfig) { this.dataConfig = dataConfig; }
    
    public HardwareConfig getHardwareConfig() { return hardwareConfig; }
    public void setHardwareConfig(HardwareConfig hardwareConfig) { this.hardwareConfig = hardwareConfig; }
    
    /**
     * 基础训练配置
     */
    public static class BaseConfig {
        private double learningRate;
        private int batchSize;
        private int maxEpochs;
        private int warmupSteps;
        private double weightDecay;
        private int gradientAccumulationSteps;
        private double maxGradNorm;
        private String lrSchedulerType;
        private int saveSteps;
        private int evalSteps;
        private int loggingSteps;
        private int saveTotalLimit;
        private boolean loadBestModelAtEnd;
        private String metricForBestModel;
        private boolean greaterIsBetter;
        
        // Getters and Setters
        public double getLearningRate() { return learningRate; }
        public void setLearningRate(double learningRate) { this.learningRate = learningRate; }
        
        public int getBatchSize() { return batchSize; }
        public void setBatchSize(int batchSize) { this.batchSize = batchSize; }
        
        public int getMaxEpochs() { return maxEpochs; }
        public void setMaxEpochs(int maxEpochs) { this.maxEpochs = maxEpochs; }
        
        // ... 其他getter和setter方法
    }
    
    /**
     * 多任务学习配置
     */
    public static class MultiTaskConfig {
        private List<TaskConfig> tasks;
        private String lossAggregation;
        private int earlyStoppingPatience;
        private double minImprovement;
        
        // Getters and Setters
        public List<TaskConfig> getTasks() { return tasks; }
        public void setTasks(List<TaskConfig> tasks) { this.tasks = tasks; }
        
        public String getLossAggregation() { return lossAggregation; }
        public void setLossAggregation(String lossAggregation) { this.lossAggregation = lossAggregation; }
        
        public int getEarlyStoppingPatience() { return earlyStoppingPatience; }
        public void setEarlyStoppingPatience(int earlyStoppingPatience) { this.earlyStoppingPatience = earlyStoppingPatience; }
        
        public double getMinImprovement() { return minImprovement; }
        public void setMinImprovement(double minImprovement) { this.minImprovement = minImprovement; }
    }
    
    /**
     * 任务配置
     */
    public static class TaskConfig {
        private String name;
        private double weight;
        private double targetLoss;
        private List<String> metrics;
        
        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public double getWeight() { return weight; }
        public void setWeight(double weight) { this.weight = weight; }
        
        public double getTargetLoss() { return targetLoss; }
        public void setTargetLoss(double targetLoss) { this.targetLoss = targetLoss; }
        
        public List<String> getMetrics() { return metrics; }
        public void setMetrics(List<String> metrics) { this.metrics = metrics; }
    }
    
    /**
     * 数据配置
     */
    public static class DataConfig {
        private String trainFile;
        private String validationFile;
        private String testFile;
        private String textColumn;
        private Map<String, String> labelColumns;
        private DataProcessing dataProcessing;
        
        // Getters and Setters
        public String getTrainFile() { return trainFile; }
        public void setTrainFile(String trainFile) { this.trainFile = trainFile; }
        
        public String getValidationFile() { return validationFile; }
        public void setValidationFile(String validationFile) { this.validationFile = validationFile; }
        
        public String getTestFile() { return testFile; }
        public void setTestFile(String testFile) { this.testFile = testFile; }
        
        public String getTextColumn() { return textColumn; }
        public void setTextColumn(String textColumn) { this.textColumn = textColumn; }
        
        public Map<String, String> getLabelColumns() { return labelColumns; }
        public void setLabelColumns(Map<String, String> labelColumns) { this.labelColumns = labelColumns; }
        
        public DataProcessing getDataProcessing() { return dataProcessing; }
        public void setDataProcessing(DataProcessing dataProcessing) { this.dataProcessing = dataProcessing; }
    }
    
    /**
     * 数据处理配置
     */
    public static class DataProcessing {
        private boolean textCleaning;
        private boolean normalization;
        private boolean augmentation;
        
        // Getters and Setters
        public boolean isTextCleaning() { return textCleaning; }
        public void setTextCleaning(boolean textCleaning) { this.textCleaning = textCleaning; }
        
        public boolean isNormalization() { return normalization; }
        public void setNormalization(boolean normalization) { this.normalization = normalization; }
        
        public boolean isAugmentation() { return augmentation; }
        public void setAugmentation(boolean augmentation) { this.augmentation = augmentation; }
    }
    
    /**
     * 硬件配置
     */
    public static class HardwareConfig {
        private String device;
        private int numGpus;
        private String mixedPrecision;
        private boolean gradientCheckpointing;
        private int dataloaderNumWorkers;
        
        // Getters and Setters
        public String getDevice() { return device; }
        public void setDevice(String device) { this.device = device; }
        
        public int getNumGpus() { return numGpus; }
        public void setNumGpus(int numGpus) { this.numGpus = numGpus; }
        
        public String getMixedPrecision() { return mixedPrecision; }
        public void setMixedPrecision(String mixedPrecision) { this.mixedPrecision = mixedPrecision; }
        
        public boolean isGradientCheckpointing() { return gradientCheckpointing; }
        public void setGradientCheckpointing(boolean gradientCheckpointing) { this.gradientCheckpointing = gradientCheckpointing; }
        
        public int getDataloaderNumWorkers() { return dataloaderNumWorkers; }
        public void setDataloaderNumWorkers(int dataloaderNumWorkers) { this.dataloaderNumWorkers = dataloaderNumWorkers; }
    }
}
```

## 3. 配置验证和加载

### 3.1 ConfigValidator.java

```java
package com.ecoenvironment.review.config;

import org.springframework.stereotype.Component;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.Set;

/**
 * 配置验证器
 */
@Component
public class ConfigValidator {
    
    private final Validator validator;
    
    public ConfigValidator(Validator validator) {
        this.validator = validator;
    }
    
    /**
     * 验证训练配置
     */
    public ValidationResult validateTrainingConfig(TrainingConfig config) {
        Set<ConstraintViolation<TrainingConfig>> violations = validator.validate(config);
        
        ValidationResult result = new ValidationResult();
        if (!violations.isEmpty()) {
            result.setValid(false);
            for (ConstraintViolation<TrainingConfig> violation : violations) {
                result.addError(violation.getPropertyPath() + ": " + violation.getMessage());
            }
        } else {
            result.setValid(true);
        }
        
        return result;
    }
    
    /**
     * 验证模型配置
     */
    public ValidationResult validateModelConfig(Map<String, Object> config) {
        ValidationResult result = new ValidationResult();
        
        // 检查必需字段
        String[] requiredFields = {"model_name", "model_type", "vocab_size", "hidden_size"};
        for (String field : requiredFields) {
            if (!config.containsKey(field)) {
                result.setValid(false);
                result.addError("Missing required field: " + field);
            }
        }
        
        // 检查字段类型和范围
        if (config.containsKey("vocab_size")) {
            Object vocabSize = config.get("vocab_size");
            if (!(vocabSize instanceof Integer) || (Integer) vocabSize <= 0) {
                result.setValid(false);
                result.addError("vocab_size must be a positive integer");
            }
        }
        
        if (result.getErrors().isEmpty()) {
            result.setValid(true);
        }
        
        return result;
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private boolean valid;
        private List<String> errors = new ArrayList<>();
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public List<String> getErrors() { return errors; }
        public void addError(String error) { this.errors.add(error); }
    }
}
```

## 4. 配置热更新

### 4.1 ConfigReloadService.java

```java
package com.ecoenvironment.review.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;

/**
 * 配置热更新服务
 */
@Service
public class ConfigReloadService {
    
    @Autowired
    private ModelConfigManager configManager;
    
    @Autowired
    private ConfigValidator configValidator;
    
    private Instant lastModelConfigTime;
    private Instant lastTrainingConfigTime;
    private Instant lastEvaluationConfigTime;
    
    /**
     * 定时检查配置文件更新
     */
    @Scheduled(fixedRate = 30000) // 每30秒检查一次
    public void checkConfigUpdates() {
        checkModelConfigUpdate();
        checkTrainingConfigUpdate();
        checkEvaluationConfigUpdate();
    }
    
    /**
     * 检查模型配置更新
     */
    private void checkModelConfigUpdate() {
        Path configPath = Paths.get("config/model-config.json");
        if (Files.exists(configPath)) {
            try {
                Instant currentTime = Files.getLastModifiedTime(configPath).toInstant();
                if (lastModelConfigTime == null || currentTime.isAfter(lastModelConfigTime)) {
                    // 配置文件已更新，重新加载
                    configManager.loadConfigs();
                    lastModelConfigTime = currentTime;
                    System.out.println("Model config reloaded at: " + currentTime);
                }
            } catch (Exception e) {
                System.err.println("Failed to check model config update: " + e.getMessage());
            }
        }
    }
    
    /**
     * 检查训练配置更新
     */
    private void checkTrainingConfigUpdate() {
        Path configPath = Paths.get("config/training-config.json");
        if (Files.exists(configPath)) {
            try {
                Instant currentTime = Files.getLastModifiedTime(configPath).toInstant();
                if (lastTrainingConfigTime == null || currentTime.isAfter(lastTrainingConfigTime)) {
                    // 配置文件已更新，重新加载
                    configManager.loadConfigs();
                    lastTrainingConfigTime = currentTime;
                    System.out.println("Training config reloaded at: " + currentTime);
                }
            } catch (Exception e) {
                System.err.println("Failed to check training config update: " + e.getMessage());
            }
        }
    }
    
    /**
     * 检查评估配置更新
     */
    private void checkEvaluationConfigUpdate() {
        Path configPath = Paths.get("config/evaluation-config.json");
        if (Files.exists(configPath)) {
            try {
                Instant currentTime = Files.getLastModifiedTime(configPath).toInstant();
                if (lastEvaluationConfigTime == null || currentTime.isAfter(lastEvaluationConfigTime)) {
                    // 配置文件已更新，重新加载
                    configManager.loadConfigs();
                    lastEvaluationConfigTime = currentTime;
                    System.out.println("Evaluation config reloaded at: " + currentTime);
                }
            } catch (Exception e) {
                System.err.println("Failed to check evaluation config update: " + e.getMessage());
            }
        }
    }
}
```

## 5. 配置使用示例

### 5.1 在训练器中使用配置

```java
@Service
public class ReviewCaseTrainer {
    
    @Autowired
    private TrainingConfig trainingConfig;
    
    @Autowired
    private ModelConfigManager modelConfigManager;
    
    public void train() {
        // 获取训练配置
        TrainingConfig.BaseConfig baseConfig = trainingConfig.getBaseConfig();
        TrainingConfig.MultiTaskConfig multiTaskConfig = trainingConfig.getMultiTaskConfig();
        
        // 使用配置参数
        double learningRate = baseConfig.getLearningRate();
        int batchSize = baseConfig.getBatchSize();
        int maxEpochs = baseConfig.getMaxEpochs();
        
        // 获取多任务配置
        List<TrainingConfig.TaskConfig> tasks = multiTaskConfig.getTasks();
        for (TrainingConfig.TaskConfig task : tasks) {
            System.out.println("Task: " + task.getName() + ", Weight: " + task.getWeight());
        }
        
        // 开始训练
        // ... 训练逻辑
    }
}
```

## 6. 配置管理最佳实践

### 6.1 配置分层
- 环境配置：开发、测试、生产环境
- 功能配置：模型、训练、评估配置
- 业务配置：业务规则、阈值配置

### 6.2 配置安全
- 敏感信息加密存储
- 配置文件权限控制
- 配置变更审计日志

### 6.3 配置版本管理
- 使用Git管理配置文件
- 配置变更记录
- 配置回滚机制 