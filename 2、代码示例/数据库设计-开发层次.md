# 生态环境行政处罚案卷评查系统 - 数据库设计（开发层次）

## 1. 核心表结构设计

### 1.1 训练样本表（training_samples）

```sql
CREATE TABLE training_samples (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    sample_id VARCHAR(50) NOT NULL UNIQUE COMMENT '样本ID',
    text TEXT NOT NULL COMMENT '案卷文本内容',
    article_label INT NOT NULL COMMENT '法律条文标签',
    defect_label DOUBLE NOT NULL COMMENT '缺陷标签（0-无缺陷，1-有缺陷）',
    penalty_amount DOUBLE NOT NULL COMMENT '处罚金额',
    procedure_type INT NOT NULL COMMENT '程序类型（1-一般行政处罚，2-听证，3-复议，4-诉讼，5-限产停产）',
    procedure_time DOUBLE NOT NULL COMMENT '程序耗时（天）',
    article_name VARCHAR(200) COMMENT '法律条文名称',
    defect_description TEXT COMMENT '缺陷描述',
    procedure_type_name VARCHAR(100) COMMENT '程序类型名称',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    reviewer_id VARCHAR(50) COMMENT '评查员ID',
    review_score DOUBLE COMMENT '评查得分',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_sample_id (sample_id),
    INDEX idx_article_label (article_label),
    INDEX idx_defect_label (defect_label),
    INDEX idx_procedure_type (procedure_type),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='训练样本表';
```

### 1.2 生态环境知识表（environment_knowledge）

```sql
CREATE TABLE environment_knowledge (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    category VARCHAR(100) NOT NULL COMMENT '知识类别',
    term VARCHAR(200) NOT NULL COMMENT '术语名称',
    description TEXT COMMENT '术语描述',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category (category),
    INDEX idx_term (term),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生态环境知识表';
```

### 1.3 法律条文表（law_articles）

```sql
CREATE TABLE law_articles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    law_name VARCHAR(200) NOT NULL COMMENT '法律名称',
    article_number VARCHAR(50) NOT NULL COMMENT '条文编号',
    article_content TEXT NOT NULL COMMENT '条文内容',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_law_name (law_name),
    INDEX idx_article_number (article_number),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='法律条文表';
```

### 1.4 裁量基准表（discretion_standards）

```sql
CREATE TABLE discretion_standards (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    law_article_id BIGINT NOT NULL COMMENT '法律条文ID',
    violation_type VARCHAR(100) NOT NULL COMMENT '违法类型',
    severity_level VARCHAR(50) NOT NULL COMMENT '严重程度',
    min_penalty DOUBLE NOT NULL COMMENT '最低处罚金额',
    max_penalty DOUBLE NOT NULL COMMENT '最高处罚金额',
    standard_description TEXT COMMENT '裁量标准描述',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (law_article_id) REFERENCES law_articles(id),
    INDEX idx_law_article_id (law_article_id),
    INDEX idx_violation_type (violation_type),
    INDEX idx_severity_level (severity_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='裁量基准表';
```

### 1.5 司法判例表（legal_cases）

```sql
CREATE TABLE legal_cases (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    case_number VARCHAR(100) NOT NULL UNIQUE COMMENT '案件编号',
    case_title VARCHAR(300) NOT NULL COMMENT '案件标题',
    case_content TEXT NOT NULL COMMENT '案件内容',
    court_name VARCHAR(200) COMMENT '法院名称',
    judgment_date DATE COMMENT '判决日期',
    case_type VARCHAR(100) COMMENT '案件类型',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_case_number (case_number),
    INDEX idx_case_title (case_title),
    INDEX idx_court_name (court_name),
    INDEX idx_judgment_date (judgment_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='司法判例表';
```

### 1.6 模型配置表（model_configs）

```sql
CREATE TABLE model_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_name VARCHAR(100) NOT NULL UNIQUE COMMENT '配置名称',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    config_type VARCHAR(50) NOT NULL COMMENT '配置类型（training/evaluation/inference）',
    config_content JSON NOT NULL COMMENT '配置内容（JSON格式）',
    version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_name (config_name),
    INDEX idx_model_name (model_name),
    INDEX idx_config_type (config_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型配置表';
```

### 1.7 训练历史表（training_history）

```sql
CREATE TABLE training_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    training_id VARCHAR(50) NOT NULL UNIQUE COMMENT '训练ID',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    config_id BIGINT NOT NULL COMMENT '配置ID',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    total_epochs INT NOT NULL COMMENT '总轮次',
    current_epoch INT DEFAULT 0 COMMENT '当前轮次',
    training_status VARCHAR(20) DEFAULT 'RUNNING' COMMENT '训练状态',
    training_loss DOUBLE COMMENT '训练损失',
    validation_loss DOUBLE COMMENT '验证损失',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (config_id) REFERENCES model_configs(id),
    INDEX idx_training_id (training_id),
    INDEX idx_model_name (model_name),
    INDEX idx_training_status (training_status),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='训练历史表';
```

### 1.8 评估结果表（evaluation_results）

```sql
CREATE TABLE evaluation_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    evaluation_id VARCHAR(50) NOT NULL UNIQUE COMMENT '评估ID',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    training_id VARCHAR(50) COMMENT '训练ID',
    evaluation_type VARCHAR(50) NOT NULL COMMENT '评估类型',
    accuracy DOUBLE COMMENT '准确率',
    precision DOUBLE COMMENT '精确率',
    recall DOUBLE COMMENT '召回率',
    f1_score DOUBLE COMMENT 'F1分数',
    evaluation_metrics JSON COMMENT '评估指标（JSON格式）',
    evaluation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '评估时间',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_evaluation_id (evaluation_id),
    INDEX idx_model_name (model_name),
    INDEX idx_training_id (training_id),
    INDEX idx_evaluation_type (evaluation_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评估结果表';
```

## 2. 索引优化建议

### 2.1 复合索引
```sql
-- 训练样本表复合索引
CREATE INDEX idx_training_samples_composite ON training_samples(article_label, defect_label, procedure_type);

-- 法律条文表复合索引
CREATE INDEX idx_law_articles_composite ON law_articles(law_name, article_number);

-- 训练历史表复合索引
CREATE INDEX idx_training_history_composite ON training_history(model_name, training_status, start_time);
```

### 2.2 全文索引
```sql
-- 训练样本表全文索引
ALTER TABLE training_samples ADD FULLTEXT INDEX ft_text (text);

-- 法律条文表全文索引
ALTER TABLE law_articles ADD FULLTEXT INDEX ft_content (article_content);

-- 生态环境知识表全文索引
ALTER TABLE environment_knowledge ADD FULLTEXT INDEX ft_description (description);
```

## 3. 数据初始化脚本

### 3.1 基础数据插入
```sql
-- 插入生态环境知识数据
INSERT INTO environment_knowledge (category, term, description) VALUES
('污染物', 'COD', '化学需氧量，衡量水体中有机污染物含量的重要指标'),
('污染物', 'BOD', '生化需氧量，衡量水体中有机污染物被微生物分解所需氧量的指标'),
('污染物', '氨氮', '水体中氨和铵离子的总称，是水体富营养化的重要指标'),
('处罚类型', '罚款', '对违法行为人处以一定金额的金钱处罚'),
('处罚类型', '责令停产整治', '责令违法企业停止生产并进行整治的处罚措施'),
('处罚类型', '限产停产', '限制或停止违法企业生产的处罚措施');

-- 插入法律条文数据
INSERT INTO law_articles (law_name, article_number, article_content) VALUES
('《中华人民共和国环境保护法》', '第六十三条', '企业事业单位和其他生产经营者有下列行为之一，尚不构成犯罪的，除依照有关法律法规规定予以处罚外，由县级以上人民政府环境保护主管部门或者其他有关部门将案件移送公安机关，对其直接负责的主管人员和其他直接责任人员，处十日以上十五日以下拘留；情节较轻的，处五日以上十日以下拘留：'),
('《中华人民共和国水污染防治法》', '第八十三条', '违反本法规定，有下列行为之一的，由县级以上人民政府环境保护主管部门责令改正或者责令限制生产、停产整治，并处十万元以上一百万元以下的罚款；情节严重的，报经有批准权的人民政府批准，责令停业、关闭：'),
('《中华人民共和国大气污染防治法》', '第九十九条', '违反本法规定，有下列行为之一的，由县级以上人民政府环境保护主管部门责令改正或者限制生产、停产整治，并处十万元以上一百万元以下的罚款；情节严重的，报经有批准权的人民政府批准，责令停业、关闭：');
```

## 4. 数据迁移脚本

### 4.1 版本管理
```sql
-- 创建版本管理表
CREATE TABLE schema_version (
    id INT PRIMARY KEY AUTO_INCREMENT,
    version VARCHAR(20) NOT NULL UNIQUE,
    description TEXT,
    applied_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入初始版本
INSERT INTO schema_version (version, description) VALUES ('1.0.0', '初始数据库结构');
```

### 4.2 数据备份脚本
```bash
#!/bin/bash
# 数据库备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u username -p database_name > backup_${DATE}.sql
```

## 5. 性能优化建议

### 5.1 查询优化
- 使用适当的索引
- 避免SELECT *
- 使用LIMIT限制结果集
- 合理使用JOIN

### 5.2 表分区
```sql
-- 训练历史表按时间分区
ALTER TABLE training_history PARTITION BY RANGE (YEAR(start_time)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 5.3 缓存策略
- 使用Redis缓存热点数据
- 实现查询结果缓存
- 配置适当的缓存过期时间 