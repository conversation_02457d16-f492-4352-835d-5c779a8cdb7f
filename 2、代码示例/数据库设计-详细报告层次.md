# 生态环境行政处罚案卷评查系统 - 数据库设计详细报告

## 1. 系统概述

### 1.1 项目背景
生态环境行政处罚案卷评查系统是一个基于人工智能的法律文书智能分析系统，旨在通过机器学习技术对生态环境行政处罚案卷进行自动化评查，提高评查效率和准确性。

### 1.2 数据库设计目标
- 支持大规模训练数据的存储和管理
- 提供高效的数据查询和检索功能
- 确保数据完整性和一致性
- 支持系统的可扩展性和可维护性
- 满足业务需求的复杂性和多样性

### 1.3 设计原则
- **规范化设计**：遵循数据库设计范式，减少数据冗余
- **性能优化**：合理设计索引，优化查询性能
- **可扩展性**：支持未来功能扩展和数据增长
- **安全性**：确保数据安全和访问控制
- **可维护性**：便于系统维护和数据管理

## 2. 核心表结构详细设计

### 2.1 训练样本表（training_samples）

#### 2.1.1 表结构设计
```sql
CREATE TABLE training_samples (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    sample_id VARCHAR(50) NOT NULL UNIQUE COMMENT '样本唯一标识',
    text TEXT NOT NULL COMMENT '案卷文本内容，包含完整的案件描述',
    article_label INT NOT NULL COMMENT '法律条文标签，对应law_articles表的id',
    defect_label DOUBLE NOT NULL COMMENT '缺陷标签，0.0表示无缺陷，1.0表示有缺陷',
    penalty_amount DOUBLE NOT NULL COMMENT '处罚金额，单位为元',
    procedure_type INT NOT NULL COMMENT '程序类型：1-一般行政处罚，2-听证，3-复议，4-诉讼，5-限产停产',
    procedure_time DOUBLE NOT NULL COMMENT '程序耗时，单位为天',
    article_name VARCHAR(200) COMMENT '法律条文名称，冗余字段便于查询',
    defect_description TEXT COMMENT '缺陷详细描述，记录具体的缺陷内容',
    procedure_type_name VARCHAR(100) COMMENT '程序类型名称，冗余字段便于查询',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
    reviewer_id VARCHAR(50) COMMENT '评查员ID，记录数据来源',
    review_score DOUBLE COMMENT '评查得分，0-100分',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '数据状态：ACTIVE-有效，INACTIVE-无效，DELETED-已删除',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
    
    -- 索引设计
    INDEX idx_sample_id (sample_id),
    INDEX idx_article_label (article_label),
    INDEX idx_defect_label (defect_label),
    INDEX idx_procedure_type (procedure_type),
    INDEX idx_created_time (created_time),
    INDEX idx_status (status),
    INDEX idx_reviewer_id (reviewer_id),
    
    -- 复合索引
    INDEX idx_article_defect_procedure (article_label, defect_label, procedure_type),
    INDEX idx_created_status (created_time, status),
    
    -- 全文索引
    FULLTEXT INDEX ft_text (text),
    FULLTEXT INDEX ft_defect_description (defect_description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='训练样本表，存储用于模型训练的案卷数据';
```

#### 2.1.2 字段详细说明

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID | 1, 2, 3... |
| sample_id | VARCHAR(50) | NOT NULL, UNIQUE | 样本唯一标识 | TRAIN_001, TRAIN_002... |
| text | TEXT | NOT NULL | 案卷文本内容 | "某化工厂因未按规定安装污染防治设施..." |
| article_label | INT | NOT NULL | 法律条文标签 | 15, 23, 8... |
| defect_label | DOUBLE | NOT NULL | 缺陷标签 | 0.0, 1.0 |
| penalty_amount | DOUBLE | NOT NULL | 处罚金额 | 100000.0, 50000.0 |
| procedure_type | INT | NOT NULL | 程序类型 | 1, 2, 3, 4, 5 |
| procedure_time | DOUBLE | NOT NULL | 程序耗时 | 30.0, 25.0 |
| article_name | VARCHAR(200) | NULL | 法律条文名称 | "《中华人民共和国环境保护法》第六十三条" |
| defect_description | TEXT | NULL | 缺陷描述 | "缺少监测报告" |
| procedure_type_name | VARCHAR(100) | NULL | 程序类型名称 | "一般行政处罚" |
| created_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-15 10:30:00 |
| reviewer_id | VARCHAR(50) | NULL | 评查员ID | REVIEWER_001 |
| review_score | DOUBLE | NULL | 评查得分 | 95.0 |
| status | VARCHAR(20) | DEFAULT 'ACTIVE' | 状态 | ACTIVE, INACTIVE, DELETED |
| updated_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-15 10:30:00 |

#### 2.1.3 业务规则
- `sample_id`必须唯一，格式为"TRAIN_" + 6位数字
- `article_label`必须存在于`law_articles`表中
- `defect_label`只能是0.0或1.0
- `penalty_amount`必须大于等于0
- `procedure_type`只能是1-5之间的整数
- `procedure_time`必须大于0
- `review_score`必须在0-100之间

### 2.2 生态环境知识表（environment_knowledge）

#### 2.2.1 表结构设计
```sql
CREATE TABLE environment_knowledge (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    category VARCHAR(100) NOT NULL COMMENT '知识类别，如污染物、处罚类型、法律法规等',
    term VARCHAR(200) NOT NULL COMMENT '术语名称',
    description TEXT COMMENT '术语详细描述',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-有效，INACTIVE-无效',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    INDEX idx_category (category),
    INDEX idx_term (term),
    INDEX idx_status (status),
    INDEX idx_category_status (category, status),
    
    -- 全文索引
    FULLTEXT INDEX ft_description (description),
    FULLTEXT INDEX ft_term_description (term, description),
    
    -- 唯一约束
    UNIQUE KEY uk_category_term (category, term)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生态环境知识表，存储生态环境领域的专业术语和知识';
```

#### 2.2.2 字段详细说明

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID | 1, 2, 3... |
| category | VARCHAR(100) | NOT NULL | 知识类别 | "污染物", "处罚类型", "法律法规" |
| term | VARCHAR(200) | NOT NULL | 术语名称 | "COD", "BOD", "氨氮" |
| description | TEXT | NULL | 术语描述 | "化学需氧量，衡量水体中有机污染物含量的重要指标" |
| status | VARCHAR(20) | DEFAULT 'ACTIVE' | 状态 | ACTIVE, INACTIVE |
| updated_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-15 10:30:00 |

#### 2.2.3 业务规则
- `category`和`term`的组合必须唯一
- `category`不能为空，且长度不超过100个字符
- `term`不能为空，且长度不超过200个字符
- `description`可以为空，但建议填写详细描述

### 2.3 法律条文表（law_articles）

#### 2.3.1 表结构设计
```sql
CREATE TABLE law_articles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    law_name VARCHAR(200) NOT NULL COMMENT '法律名称',
    article_number VARCHAR(50) NOT NULL COMMENT '条文编号',
    article_content TEXT NOT NULL COMMENT '条文内容',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-有效，INACTIVE-无效',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    INDEX idx_law_name (law_name),
    INDEX idx_article_number (article_number),
    INDEX idx_status (status),
    INDEX idx_law_article (law_name, article_number),
    
    -- 全文索引
    FULLTEXT INDEX ft_content (article_content),
    FULLTEXT INDEX ft_law_article_content (law_name, article_number, article_content),
    
    -- 唯一约束
    UNIQUE KEY uk_law_article (law_name, article_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='法律条文表，存储相关的法律法规条文';
```

#### 2.3.2 字段详细说明

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID | 1, 2, 3... |
| law_name | VARCHAR(200) | NOT NULL | 法律名称 | "《中华人民共和国环境保护法》" |
| article_number | VARCHAR(50) | NOT NULL | 条文编号 | "第六十三条" |
| article_content | TEXT | NOT NULL | 条文内容 | "企业事业单位和其他生产经营者有下列行为之一..." |
| status | VARCHAR(20) | DEFAULT 'ACTIVE' | 状态 | ACTIVE, INACTIVE |
| updated_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-15 10:30:00 |

#### 2.3.3 业务规则
- `law_name`和`article_number`的组合必须唯一
- `law_name`不能为空，且长度不超过200个字符
- `article_number`不能为空，且长度不超过50个字符
- `article_content`不能为空

### 2.4 裁量基准表（discretion_standards）

#### 2.4.1 表结构设计
```sql
CREATE TABLE discretion_standards (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    law_article_id BIGINT NOT NULL COMMENT '法律条文ID，关联law_articles表',
    violation_type VARCHAR(100) NOT NULL COMMENT '违法类型',
    severity_level VARCHAR(50) NOT NULL COMMENT '严重程度：轻微、一般、严重、特别严重',
    min_penalty DOUBLE NOT NULL COMMENT '最低处罚金额',
    max_penalty DOUBLE NOT NULL COMMENT '最高处罚金额',
    standard_description TEXT COMMENT '裁量标准详细描述',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-有效，INACTIVE-无效',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (law_article_id) REFERENCES law_articles(id) ON DELETE CASCADE,
    
    -- 索引设计
    INDEX idx_law_article_id (law_article_id),
    INDEX idx_violation_type (violation_type),
    INDEX idx_severity_level (severity_level),
    INDEX idx_penalty_range (min_penalty, max_penalty),
    INDEX idx_status (status),
    
    -- 复合索引
    INDEX idx_law_violation_severity (law_article_id, violation_type, severity_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='裁量基准表，存储法律条文的裁量标准';
```

#### 2.4.2 字段详细说明

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID | 1, 2, 3... |
| law_article_id | BIGINT | NOT NULL, FOREIGN KEY | 法律条文ID | 1, 2, 3... |
| violation_type | VARCHAR(100) | NOT NULL | 违法类型 | "未安装污染防治设施", "超标排放" |
| severity_level | VARCHAR(50) | NOT NULL | 严重程度 | "轻微", "一般", "严重", "特别严重" |
| min_penalty | DOUBLE | NOT NULL | 最低处罚金额 | 10000.0 |
| max_penalty | DOUBLE | NOT NULL | 最高处罚金额 | 100000.0 |
| standard_description | TEXT | NULL | 裁量标准描述 | "根据违法情节、危害后果等因素确定处罚金额" |
| status | VARCHAR(20) | DEFAULT 'ACTIVE' | 状态 | ACTIVE, INACTIVE |
| updated_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-15 10:30:00 |

#### 2.4.3 业务规则
- `law_article_id`必须存在于`law_articles`表中
- `min_penalty`必须小于等于`max_penalty`
- `min_penalty`和`max_penalty`都必须大于等于0
- `severity_level`只能是预定义的值：轻微、一般、严重、特别严重

### 2.5 司法判例表（legal_cases）

#### 2.5.1 表结构设计
```sql
CREATE TABLE legal_cases (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    case_number VARCHAR(100) NOT NULL UNIQUE COMMENT '案件编号',
    case_title VARCHAR(300) NOT NULL COMMENT '案件标题',
    case_content TEXT NOT NULL COMMENT '案件内容',
    court_name VARCHAR(200) COMMENT '法院名称',
    judgment_date DATE COMMENT '判决日期',
    case_type VARCHAR(100) COMMENT '案件类型：行政诉讼、民事诉讼、刑事诉讼',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-有效，INACTIVE-无效',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    INDEX idx_case_number (case_number),
    INDEX idx_case_title (case_title),
    INDEX idx_court_name (court_name),
    INDEX idx_judgment_date (judgment_date),
    INDEX idx_case_type (case_type),
    INDEX idx_status (status),
    
    -- 复合索引
    INDEX idx_court_date (court_name, judgment_date),
    INDEX idx_type_date (case_type, judgment_date),
    
    -- 全文索引
    FULLTEXT INDEX ft_content (case_content),
    FULLTEXT INDEX ft_title_content (case_title, case_content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='司法判例表，存储相关的司法判例';
```

#### 2.5.2 字段详细说明

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID | 1, 2, 3... |
| case_number | VARCHAR(100) | NOT NULL, UNIQUE | 案件编号 | "(2023)京01行终1234号" |
| case_title | VARCHAR(300) | NOT NULL | 案件标题 | "某化工厂诉环保局行政处罚案" |
| case_content | TEXT | NOT NULL | 案件内容 | "原告某化工厂不服被告环保局作出的行政处罚决定..." |
| court_name | VARCHAR(200) | NULL | 法院名称 | "北京市第一中级人民法院" |
| judgment_date | DATE | NULL | 判决日期 | 2023-12-15 |
| case_type | VARCHAR(100) | NULL | 案件类型 | "行政诉讼", "民事诉讼", "刑事诉讼" |
| status | VARCHAR(20) | DEFAULT 'ACTIVE' | 状态 | ACTIVE, INACTIVE |
| updated_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-15 10:30:00 |

#### 2.5.3 业务规则
- `case_number`必须唯一
- `case_title`不能为空，且长度不超过300个字符
- `case_content`不能为空
- `judgment_date`不能晚于当前日期

### 2.6 模型配置表（model_configs）

#### 2.6.1 表结构设计
```sql
CREATE TABLE model_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    config_name VARCHAR(100) NOT NULL UNIQUE COMMENT '配置名称',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    config_type VARCHAR(50) NOT NULL COMMENT '配置类型：training-训练配置，evaluation-评估配置，inference-推理配置',
    config_content JSON NOT NULL COMMENT '配置内容，JSON格式存储',
    version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-有效，INACTIVE-无效',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    INDEX idx_config_name (config_name),
    INDEX idx_model_name (model_name),
    INDEX idx_config_type (config_type),
    INDEX idx_status (status),
    INDEX idx_version (version),
    
    -- 复合索引
    INDEX idx_model_type_status (model_name, config_type, status),
    INDEX idx_name_version (config_name, version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型配置表，存储模型相关的配置信息';
```

#### 2.6.2 字段详细说明

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID | 1, 2, 3... |
| config_name | VARCHAR(100) | NOT NULL, UNIQUE | 配置名称 | "chatglm3-6b-training-config" |
| model_name | VARCHAR(100) | NOT NULL | 模型名称 | "chatglm3-6b" |
| config_type | VARCHAR(50) | NOT NULL | 配置类型 | "training", "evaluation", "inference" |
| config_content | JSON | NOT NULL | 配置内容 | {"learning_rate": 2e-5, "batch_size": 8} |
| version | VARCHAR(20) | DEFAULT '1.0' | 版本号 | "1.0", "1.1", "2.0" |
| status | VARCHAR(20) | DEFAULT 'ACTIVE' | 状态 | ACTIVE, INACTIVE |
| created_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-15 10:30:00 |
| updated_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-15 10:30:00 |

#### 2.6.3 业务规则
- `config_name`必须唯一
- `config_type`只能是预定义的值：training、evaluation、inference
- `config_content`必须是有效的JSON格式
- `version`格式为"主版本.次版本.修订版本"

### 2.7 训练历史表（training_history）

#### 2.7.1 表结构设计
```sql
CREATE TABLE training_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    training_id VARCHAR(50) NOT NULL UNIQUE COMMENT '训练ID',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    config_id BIGINT NOT NULL COMMENT '配置ID，关联model_configs表',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    total_epochs INT NOT NULL COMMENT '总轮次',
    current_epoch INT DEFAULT 0 COMMENT '当前轮次',
    training_status VARCHAR(20) DEFAULT 'RUNNING' COMMENT '训练状态：RUNNING-运行中，COMPLETED-已完成，FAILED-失败，CANCELLED-已取消',
    training_loss DOUBLE NULL COMMENT '训练损失',
    validation_loss DOUBLE NULL COMMENT '验证损失',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (config_id) REFERENCES model_configs(id) ON DELETE CASCADE,
    
    -- 索引设计
    INDEX idx_training_id (training_id),
    INDEX idx_model_name (model_name),
    INDEX idx_training_status (training_status),
    INDEX idx_start_time (start_time),
    INDEX idx_config_id (config_id),
    
    -- 复合索引
    INDEX idx_model_status_time (model_name, training_status, start_time),
    INDEX idx_status_time (training_status, start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='训练历史表，记录模型训练的历史信息';
```

#### 2.7.2 字段详细说明

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID | 1, 2, 3... |
| training_id | VARCHAR(50) | NOT NULL, UNIQUE | 训练ID | "TRAIN_20240115_001" |
| model_name | VARCHAR(100) | NOT NULL | 模型名称 | "chatglm3-6b" |
| config_id | BIGINT | NOT NULL, FOREIGN KEY | 配置ID | 1, 2, 3... |
| start_time | TIMESTAMP | NOT NULL | 开始时间 | 2024-01-15 10:30:00 |
| end_time | TIMESTAMP | NULL | 结束时间 | 2024-01-15 18:30:00 |
| total_epochs | INT | NOT NULL | 总轮次 | 10 |
| current_epoch | INT | DEFAULT 0 | 当前轮次 | 5 |
| training_status | VARCHAR(20) | DEFAULT 'RUNNING' | 训练状态 | RUNNING, COMPLETED, FAILED, CANCELLED |
| training_loss | DOUBLE | NULL | 训练损失 | 0.15 |
| validation_loss | DOUBLE | NULL | 验证损失 | 0.12 |
| created_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-15 10:30:00 |
| updated_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-15 10:30:00 |

#### 2.7.3 业务规则
- `training_id`必须唯一，格式为"TRAIN_" + 日期 + "_" + 序号
- `config_id`必须存在于`model_configs`表中
- `total_epochs`必须大于0
- `current_epoch`必须小于等于`total_epochs`
- `training_status`只能是预定义的值：RUNNING、COMPLETED、FAILED、CANCELLED
- `start_time`不能晚于`end_time`

### 2.8 评估结果表（evaluation_results）

#### 2.8.1 表结构设计
```sql
CREATE TABLE evaluation_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    evaluation_id VARCHAR(50) NOT NULL UNIQUE COMMENT '评估ID',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    training_id VARCHAR(50) NULL COMMENT '训练ID，关联training_history表',
    evaluation_type VARCHAR(50) NOT NULL COMMENT '评估类型：accuracy-准确率评估，performance-性能评估，comprehensive-综合评估',
    accuracy DOUBLE NULL COMMENT '准确率',
    precision DOUBLE NULL COMMENT '精确率',
    recall DOUBLE NULL COMMENT '召回率',
    f1_score DOUBLE NULL COMMENT 'F1分数',
    evaluation_metrics JSON NULL COMMENT '评估指标，JSON格式存储',
    evaluation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '评估时间',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引设计
    INDEX idx_evaluation_id (evaluation_id),
    INDEX idx_model_name (model_name),
    INDEX idx_training_id (training_id),
    INDEX idx_evaluation_type (evaluation_type),
    INDEX idx_evaluation_time (evaluation_time),
    
    -- 复合索引
    INDEX idx_model_type_time (model_name, evaluation_type, evaluation_time),
    INDEX idx_training_type (training_id, evaluation_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评估结果表，存储模型评估的结果';
```

#### 2.8.2 字段详细说明

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID | 1, 2, 3... |
| evaluation_id | VARCHAR(50) | NOT NULL, UNIQUE | 评估ID | "EVAL_20240115_001" |
| model_name | VARCHAR(100) | NOT NULL | 模型名称 | "chatglm3-6b" |
| training_id | VARCHAR(50) | NULL | 训练ID | "TRAIN_20240115_001" |
| evaluation_type | VARCHAR(50) | NOT NULL | 评估类型 | "accuracy", "performance", "comprehensive" |
| accuracy | DOUBLE | NULL | 准确率 | 0.95 |
| precision | DOUBLE | NULL | 精确率 | 0.92 |
| recall | DOUBLE | NULL | 召回率 | 0.88 |
| f1_score | DOUBLE | NULL | F1分数 | 0.90 |
| evaluation_metrics | JSON | NULL | 评估指标 | {"mse": 0.05, "mae": 0.03} |
| evaluation_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 评估时间 | 2024-01-15 18:30:00 |
| created_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-15 18:30:00 |

#### 2.8.3 业务规则
- `evaluation_id`必须唯一，格式为"EVAL_" + 日期 + "_" + 序号
- `evaluation_type`只能是预定义的值：accuracy、performance、comprehensive
- `accuracy`、`precision`、`recall`、`f1_score`必须在0-1之间
- `evaluation_metrics`必须是有效的JSON格式

## 3. 索引优化策略

### 3.1 索引设计原则
1. **查询频率优先**：为经常查询的字段创建索引
2. **选择性原则**：优先为选择性高的字段创建索引
3. **复合索引优化**：根据查询模式设计复合索引
4. **避免过度索引**：避免创建不必要的索引，影响写入性能

### 3.2 复合索引设计
```sql
-- 训练样本表复合索引
CREATE INDEX idx_training_samples_composite ON training_samples(article_label, defect_label, procedure_type);

-- 法律条文表复合索引
CREATE INDEX idx_law_articles_composite ON law_articles(law_name, article_number);

-- 训练历史表复合索引
CREATE INDEX idx_training_history_composite ON training_history(model_name, training_status, start_time);

-- 评估结果表复合索引
CREATE INDEX idx_evaluation_results_composite ON evaluation_results(model_name, evaluation_type, evaluation_time);
```

### 3.3 全文索引设计
```sql
-- 训练样本表全文索引
ALTER TABLE training_samples ADD FULLTEXT INDEX ft_text (text);

-- 法律条文表全文索引
ALTER TABLE law_articles ADD FULLTEXT INDEX ft_content (article_content);

-- 生态环境知识表全文索引
ALTER TABLE environment_knowledge ADD FULLTEXT INDEX ft_description (description);

-- 司法判例表全文索引
ALTER TABLE legal_cases ADD FULLTEXT INDEX ft_content (case_content);
```

## 4. 数据初始化和管理

### 4.1 基础数据插入
```sql
-- 插入生态环境知识数据
INSERT INTO environment_knowledge (category, term, description) VALUES
('污染物', 'COD', '化学需氧量，衡量水体中有机污染物含量的重要指标'),
('污染物', 'BOD', '生化需氧量，衡量水体中有机污染物被微生物分解所需氧量的指标'),
('污染物', '氨氮', '水体中氨和铵离子的总称，是水体富营养化的重要指标'),
('污染物', '总磷', '水体中各种形态磷的总和，是水体富营养化的重要指标'),
('污染物', '总氮', '水体中各种形态氮的总和，是水体富营养化的重要指标'),
('处罚类型', '罚款', '对违法行为人处以一定金额的金钱处罚'),
('处罚类型', '责令停产整治', '责令违法企业停止生产并进行整治的处罚措施'),
('处罚类型', '限产停产', '限制或停止违法企业生产的处罚措施'),
('处罚类型', '责令改正', '责令违法行为人改正违法行为的处罚措施'),
('处罚类型', '责令停业关闭', '责令违法企业停业或关闭的处罚措施');

-- 插入法律条文数据
INSERT INTO law_articles (law_name, article_number, article_content) VALUES
('《中华人民共和国环境保护法》', '第六十三条', '企业事业单位和其他生产经营者有下列行为之一，尚不构成犯罪的，除依照有关法律法规规定予以处罚外，由县级以上人民政府环境保护主管部门或者其他有关部门将案件移送公安机关，对其直接负责的主管人员和其他直接责任人员，处十日以上十五日以下拘留；情节较轻的，处五日以上十日以下拘留：'),
('《中华人民共和国水污染防治法》', '第八十三条', '违反本法规定，有下列行为之一的，由县级以上人民政府环境保护主管部门责令改正或者责令限制生产、停产整治，并处十万元以上一百万元以下的罚款；情节严重的，报经有批准权的人民政府批准，责令停业、关闭：'),
('《中华人民共和国大气污染防治法》', '第九十九条', '违反本法规定，有下列行为之一的，由县级以上人民政府环境保护主管部门责令改正或者限制生产、停产整治，并处十万元以上一百万元以下的罚款；情节严重的，报经有批准权的人民政府批准，责令停业、关闭：'),
('《中华人民共和国固体废物污染环境防治法》', '第一百一十二条', '违反本法规定，有下列行为之一的，由县级以上人民政府环境保护主管部门责令改正，处以罚款，没收违法所得；情节严重的，报经有批准权的人民政府批准，可以责令停业或者关闭：'),
('《中华人民共和国环境影响评价法》', '第三十一条', '建设单位未依法报批建设项目环境影响报告书、报告表，或者未依照本法第二十四条的规定重新报批或者报请重新审核环境影响报告书、报告表，擅自开工建设的，由县级以上环境保护行政主管部门责令停止建设，根据违法情节和危害后果，处建设项目总投资额百分之一以上百分之五以下的罚款，并可以责令恢复原状；对建设单位直接负责的主管人员和其他直接责任人员，依法给予行政处分。');
```

### 4.2 数据迁移脚本
```sql
-- 创建版本管理表
CREATE TABLE schema_version (
    id INT PRIMARY KEY AUTO_INCREMENT,
    version VARCHAR(20) NOT NULL UNIQUE,
    description TEXT,
    applied_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入初始版本
INSERT INTO schema_version (version, description) VALUES ('1.0.0', '初始数据库结构');

-- 数据备份脚本
DELIMITER $$
CREATE PROCEDURE backup_database()
BEGIN
    DECLARE backup_file VARCHAR(255);
    SET backup_file = CONCAT('/backup/backup_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'), '.sql');
    
    -- 这里需要根据具体的数据库备份工具来实现
    -- 例如使用mysqldump命令
    SET @sql = CONCAT('mysqldump -u username -p database_name > ', backup_file);
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;
```

## 5. 性能优化建议

### 5.1 查询优化
1. **避免SELECT ***：只查询需要的字段
2. **使用LIMIT**：限制结果集大小
3. **合理使用JOIN**：避免多表JOIN，使用适当的JOIN类型
4. **使用EXPLAIN**：分析查询执行计划

### 5.2 表分区
```sql
-- 训练历史表按时间分区
ALTER TABLE training_history PARTITION BY RANGE (YEAR(start_time)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 评估结果表按时间分区
ALTER TABLE evaluation_results PARTITION BY RANGE (YEAR(evaluation_time)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 5.3 缓存策略
1. **Redis缓存**：缓存热点数据和查询结果
2. **查询缓存**：缓存频繁查询的结果
3. **配置缓存**：缓存模型配置信息
4. **会话缓存**：缓存用户会话信息

## 6. 安全性和合规性

### 6.1 数据安全
1. **数据加密**：敏感数据加密存储
2. **访问控制**：基于角色的访问控制
3. **审计日志**：记录数据访问和修改日志
4. **数据备份**：定期备份重要数据

### 6.2 合规性要求
1. **数据隐私**：遵守数据保护法规
2. **模型治理**：建立模型治理规范
3. **审计追踪**：完整的审计追踪机制
4. **合规性检查**：定期进行合规性检查

## 7. 监控和维护

### 7.1 性能监控
1. **查询性能**：监控慢查询和查询性能
2. **存储性能**：监控存储空间和I/O性能
3. **连接性能**：监控数据库连接数和连接池状态
4. **缓存性能**：监控缓存命中率和缓存性能

### 7.2 维护计划
1. **定期备份**：每日备份重要数据
2. **索引维护**：定期重建和优化索引
3. **数据清理**：定期清理过期和无效数据
4. **性能调优**：定期进行性能调优

## 8. 总结

本数据库设计详细报告提供了生态环境行政处罚案卷评查系统的完整数据库设计方案。通过合理的表结构设计、索引优化、数据管理和性能优化，系统能够支持大规模数据的存储和处理，满足业务需求的复杂性和多样性。

设计遵循了数据库设计的最佳实践，确保了数据的完整性、一致性和安全性，同时考虑了系统的可扩展性和可维护性。通过实施本设计方案，系统将能够高效地支持模型训练、评估和推理等核心功能。 