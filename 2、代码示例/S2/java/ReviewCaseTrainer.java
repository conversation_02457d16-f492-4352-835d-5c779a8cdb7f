package com.ecoenvironment.review.s2;

import java.util.*;
import java.util.logging.Logger;

/**
 * 评查案例训练器
 * 实现S2评查专项微调的训练逻辑
 */
public class ReviewCaseTrainer {
    
    private static final Logger logger = Logger.getLogger(ReviewCaseTrainer.class.getName());
    
    private MultiTaskModel model;
    private FineTuningConfig config;
    private double bestLoss = Double.MAX_VALUE;
    private int patienceCounter = 0;
    private List<Map<String, Object>> trainingHistory;
    
    /**
     * 构造函数
     */
    public ReviewCaseTrainer(MultiTaskModel model, FineTuningConfig config) {
        this.model = model;
        this.config = config;
        this.trainingHistory = new ArrayList<>();
    }
    
    /**
     * 训练模型
     */
    public void train(List<ReviewCase> trainData, List<ReviewCase> valData) {
        logger.info("开始训练评查专项微调模型");
        logger.info("训练数据量: " + trainData.size());
        logger.info("验证数据量: " + valData.size());
        
        for (int epoch = 0; epoch < config.getMaxEpochs(); epoch++) {
            logger.info("Epoch " + (epoch + 1) + "/" + config.getMaxEpochs());
            
            // 训练阶段
            Map<String, Double> trainLosses = trainEpoch(trainData);
            
            // 验证阶段
            Map<String, Double> valLosses = validateEpoch(valData);
            
            // 打印训练信息
            logEpochInfo(epoch, trainLosses, valLosses);
            
            // 记录训练历史
            recordTrainingHistory(epoch, trainLosses, valLosses);
            
            // 早停检查
            if (shouldStopEarly(valLosses)) {
                logger.info("触发早停机制，停止训练");
                break;
            }
        }
        
        logger.info("训练完成");
        printTrainingSummary();
    }
    
    /**
     * 训练一个epoch
     */
    private Map<String, Double> trainEpoch(List<ReviewCase> trainData) {
        logger.info("开始训练epoch");
        
        Map<String, Double> totalLosses = new HashMap<>();
        totalLosses.put("article", 0.0);
        totalLosses.put("defect", 0.0);
        totalLosses.put("penalty", 0.0);
        totalLosses.put("procedure_class", 0.0);
        totalLosses.put("procedure_time", 0.0);
        totalLosses.put("total_loss", 0.0);
        
        int batchCount = 0;
        int batchSize = config.getBatchSize();
        
        // 分批训练
        for (int i = 0; i < trainData.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, trainData.size());
            List<ReviewCase> batch = trainData.subList(i, endIndex);
            
            // 处理批次数据
            Map<String, Double> batchLosses = processBatch(batch, true);
            
            // 累计损失
            for (Map.Entry<String, Double> entry : batchLosses.entrySet()) {
                totalLosses.put(entry.getKey(), totalLosses.get(entry.getKey()) + entry.getValue());
            }
            
            batchCount++;
            
            // 打印进度
            if (batchCount % 100 == 0) {
                logger.info("已处理 " + batchCount + " 个批次");
            }
        }
        
        // 计算平均损失
        for (Map.Entry<String, Double> entry : totalLosses.entrySet()) {
            totalLosses.put(entry.getKey(), entry.getValue() / batchCount);
        }
        
        return totalLosses;
    }
    
    /**
     * 验证一个epoch
     */
    private Map<String, Double> validateEpoch(List<ReviewCase> valData) {
        logger.info("开始验证epoch");
        
        Map<String, Double> totalLosses = new HashMap<>();
        totalLosses.put("article", 0.0);
        totalLosses.put("defect", 0.0);
        totalLosses.put("penalty", 0.0);
        totalLosses.put("procedure_class", 0.0);
        totalLosses.put("procedure_time", 0.0);
        totalLosses.put("total_loss", 0.0);
        
        int batchCount = 0;
        int batchSize = config.getBatchSize();
        
        // 分批验证
        for (int i = 0; i < valData.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, valData.size());
            List<ReviewCase> batch = valData.subList(i, endIndex);
            
            // 处理批次数据（不更新参数）
            Map<String, Double> batchLosses = processBatch(batch, false);
            
            // 累计损失
            for (Map.Entry<String, Double> entry : batchLosses.entrySet()) {
                totalLosses.put(entry.getKey(), totalLosses.get(entry.getKey()) + entry.getValue());
            }
            
            batchCount++;
        }
        
        // 计算平均损失
        for (Map.Entry<String, Double> entry : totalLosses.entrySet()) {
            totalLosses.put(entry.getKey(), entry.getValue() / batchCount);
        }
        
        return totalLosses;
    }
    
    /**
     * 处理批次数据
     */
    private Map<String, Double> processBatch(List<ReviewCase> batch, boolean isTraining) {
        Map<String, Double> batchLosses = new HashMap<>();
        
        // 准备批次数据
        List<String> inputIds = new ArrayList<>();
        List<Integer> attentionMask = new ArrayList<>();
        Map<String, Object> labels = new HashMap<>();
        
        for (ReviewCase reviewCase : batch) {
            // 模拟文本编码
            inputIds.add(reviewCase.getText());
            attentionMask.add(1);
            
            // 准备标签
            if (!labels.containsKey("article")) {
                labels.put("article", new ArrayList<Integer>());
                labels.put("defect", new ArrayList<Double>());
                labels.put("penalty", new ArrayList<Double>());
                labels.put("procedure_type", new ArrayList<Integer>());
                labels.put("procedure_time", new ArrayList<Double>());
            }
            
            ((List<Integer>) labels.get("article")).add(reviewCase.getArticleLabel());
            ((List<Double>) labels.get("defect")).add(reviewCase.getDefectLabel());
            ((List<Double>) labels.get("penalty")).add(reviewCase.getPenaltyAmount());
            ((List<Integer>) labels.get("procedure_type")).add(reviewCase.getProcedureType());
            ((List<Double>) labels.get("procedure_time")).add(reviewCase.getProcedureTime());
        }
        
        // 模型前向传播
        Map<String, Object> outputs = model.forward(inputIds, attentionMask, labels);
        
        // 计算损失
        if (outputs.containsKey("losses")) {
            Map<String, Double> losses = (Map<String, Double>) outputs.get("losses");
            batchLosses.putAll(losses);
        }
        
        return batchLosses;
    }
    
    /**
     * 打印训练信息
     */
    private void logEpochInfo(int epoch, Map<String, Double> trainLosses, Map<String, Double> valLosses) {
        logger.info("Epoch " + (epoch + 1) + " 训练损失: " + String.format("%.4f", trainLosses.get("total_loss")));
        logger.info("Epoch " + (epoch + 1) + " 验证损失: " + String.format("%.4f", valLosses.get("total_loss")));
        logger.info("  法律条款分类损失: " + String.format("%.4f", valLosses.get("article")));
        logger.info("  缺陷检测损失: " + String.format("%.4f", valLosses.get("defect")));
        logger.info("  处罚金额预测损失: " + String.format("%.4f", valLosses.get("penalty")));
        logger.info("  流程分类损失: " + String.format("%.4f", valLosses.get("procedure_class")));
        logger.info("  流程时间预测损失: " + String.format("%.4f", valLosses.get("procedure_time")));
    }
    
    /**
     * 记录训练历史
     */
    private void recordTrainingHistory(int epoch, Map<String, Double> trainLosses, Map<String, Double> valLosses) {
        Map<String, Object> record = new HashMap<>();
        record.put("epoch", epoch);
        record.put("train_losses", new HashMap<>(trainLosses));
        record.put("val_losses", new HashMap<>(valLosses));
        trainingHistory.add(record);
    }
    
    /**
     * 检查是否应该早停
     */
    private boolean shouldStopEarly(Map<String, Double> valLosses) {
        double currentLoss = valLosses.get("total_loss");
        
        // 检查是否达到目标值
        if (valLosses.get("article") <= config.getTargetArticleLoss() &&
            valLosses.get("defect") <= config.getTargetDefectLoss() &&
            valLosses.get("penalty") <= config.getTargetPenaltyLoss() &&
            valLosses.get("procedure_class") + valLosses.get("procedure_time") <= config.getTargetProcedureLoss()) {
            logger.info("达到目标损失值，停止训练");
            return true;
        }
        
        // 检查是否有改善
        if (currentLoss < bestLoss - config.getMinImprovement()) {
            bestLoss = currentLoss;
            patienceCounter = 0;
            logger.info("发现更好的模型，重置耐心计数器");
        } else {
            patienceCounter++;
            logger.info("耐心计数器: " + patienceCounter + "/" + config.getEarlyStoppingPatience());
        }
        
        // 检查是否超过耐心值
        if (patienceCounter >= config.getEarlyStoppingPatience()) {
            logger.info("连续" + config.getEarlyStoppingPatience() + "轮无改善，触发早停");
            return true;
        }
        
        return false;
    }
    
    /**
     * 打印训练摘要
     */
    private void printTrainingSummary() {
        if (trainingHistory.isEmpty()) {
            logger.info("暂无训练历史");
            return;
        }
        
        Map<String, Object> latestRecord = trainingHistory.get(trainingHistory.size() - 1);
        Map<String, Double> latestValLosses = (Map<String, Double>) latestRecord.get("val_losses");
        
        logger.info("训练摘要:");
        logger.info("总训练轮数: " + trainingHistory.size());
        logger.info("最佳损失: " + String.format("%.4f", bestLoss));
        logger.info("最终验证损失: " + String.format("%.4f", latestValLosses.get("total_loss")));
        logger.info("最终法律条款分类损失: " + String.format("%.4f", latestValLosses.get("article")));
        logger.info("最终缺陷检测损失: " + String.format("%.4f", latestValLosses.get("defect")));
        logger.info("最终处罚金额预测损失: " + String.format("%.4f", latestValLosses.get("penalty")));
        logger.info("最终流程损失: " + String.format("%.4f", latestValLosses.get("procedure_class") + latestValLosses.get("procedure_time")));
    }
    
    /**
     * 获取训练历史
     */
    public List<Map<String, Object>> getTrainingHistory() {
        return new ArrayList<>(trainingHistory);
    }
    
    /**
     * 获取最佳epoch
     */
    public int getBestEpoch() {
        if (trainingHistory.isEmpty()) {
            return -1;
        }
        
        int bestEpoch = 0;
        double bestLoss = Double.MAX_VALUE;
        
        for (Map<String, Object> record : trainingHistory) {
            Map<String, Double> valLosses = (Map<String, Double>) record.get("val_losses");
            double totalLoss = valLosses.get("total_loss");
            
            if (totalLoss < bestLoss) {
                bestLoss = totalLoss;
                bestEpoch = (Integer) record.get("epoch");
            }
        }
        
        return bestEpoch;
    }
    
    /**
     * 保存模型
     */
    public void saveModel(String modelPath) {
        logger.info("保存模型到: " + modelPath);
        // 这里应该实现模型保存逻辑
        // 由于是示例代码，这里只是打印日志
    }
    
    /**
     * 加载模型
     */
    public void loadModel(String modelPath) {
        logger.info("从 " + modelPath + " 加载模型");
        // 这里应该实现模型加载逻辑
        // 由于是示例代码，这里只是打印日志
    }
    
    /**
     * 测试训练器
     */
    public static void main(String[] args) {
        // 创建配置
        FineTuningConfig config = new FineTuningConfig();
        config.setModelName("chatglm3-6b");
        config.setLearningRate(2e-5);
        config.setBatchSize(8);
        config.setMaxEpochs(5);
        config.setWeightArticle(0.4);
        config.setWeightDefect(0.3);
        config.setWeightPenalty(0.2);
        config.setWeightProcedure(0.1);
        
        // 创建模型
        MultiTaskModel model = new MultiTaskModel(config);
        
        // 创建训练器
        ReviewCaseTrainer trainer = new ReviewCaseTrainer(model, config);
        
        // 创建模拟数据
        List<ReviewCase> trainData = createMockData(100);
        List<ReviewCase> valData = createMockData(20);
        
        // 训练模型
        trainer.train(trainData, valData);
        
        logger.info("训练器测试完成");
    }
    
    /**
     * 创建模拟数据
     */
    private static List<ReviewCase> createMockData(int size) {
        List<ReviewCase> data = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            data.add(new ReviewCase(
                "模拟案卷文本内容" + i,
                (int) (Math.random() * 100),
                Math.random() > 0.5 ? 1.0 : 0.0,
                Math.random() * 10000,
                (int) (Math.random() * 10),
                Math.random() * 365
            ));
        }
        return data;
    }
} 