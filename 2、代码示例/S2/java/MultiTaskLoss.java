package com.ecoenvironment.review.s2;

import java.util.*;
import java.util.logging.Logger;

/**
 * 多任务损失函数实现
 * 实现S2评查专项微调中的加权多任务损失函数
 */
public class MultiTaskLoss {
    
    private static final Logger logger = Logger.getLogger(MultiTaskLoss.class.getName());
    
    private Map<String, Double> weights;
    
    /**
     * 构造函数
     */
    public MultiTaskLoss() {
        this.weights = new HashMap<>();
        this.weights.put("article", 0.4);
        this.weights.put("defect", 0.3);
        this.weights.put("penalty", 0.2);
        this.weights.put("procedure", 0.1);
        
        // 验证权重和为1
        double totalWeight = weights.values().stream().mapToDouble(Double::doubleValue).sum();
        if (Math.abs(totalWeight - 1.0) > 1e-6) {
            throw new IllegalArgumentException("权重和必须为1，当前为" + totalWeight);
        }
    }
    
    /**
     * 构造函数（自定义权重）
     */
    public MultiTaskLoss(Map<String, Double> weights) {
        this.weights = new HashMap<>(weights);
        
        // 验证权重和为1
        double totalWeight = this.weights.values().stream().mapToDouble(Double::doubleValue).sum();
        if (Math.abs(totalWeight - 1.0) > 1e-6) {
            throw new IllegalArgumentException("权重和必须为1，当前为" + totalWeight);
        }
    }
    
    /**
     * 计算多任务损失
     */
    public Map<String, Double> calculateLosses(Map<String, Object> outputs, Map<String, Object> labels) {
        Map<String, Double> losses = new HashMap<>();
        
        // 1. 法律条款分类损失 (Larticle)
        if (outputs.containsKey("article_logits") && labels.containsKey("article")) {
            losses.put("article", computeArticleLoss(
                (List<Double>) outputs.get("article_logits"), 
                (Integer) labels.get("article")
            ));
        }
        
        // 2. 缺陷检测损失 (Ldefect)
        if (outputs.containsKey("defect_logits") && labels.containsKey("defect")) {
            losses.put("defect", computeDefectLoss(
                (List<Double>) outputs.get("defect_logits"), 
                (Double) labels.get("defect")
            ));
        }
        
        // 3. 处罚金额预测损失 (Lpenalty)
        if (outputs.containsKey("penalty_pred") && labels.containsKey("penalty")) {
            losses.put("penalty", computePenaltyLoss(
                (List<Double>) outputs.get("penalty_pred"), 
                (Double) labels.get("penalty")
            ));
        }
        
        // 4. 流程损失 (Lprocedure)
        if ((outputs.containsKey("procedure_class_logits") || outputs.containsKey("procedure_time_pred")) &&
            (labels.containsKey("procedure_type") || labels.containsKey("procedure_time"))) {
            losses.put("procedure", computeProcedureLoss(outputs, labels));
        }
        
        // 计算加权总损失
        double totalLoss = computeWeightedTotalLoss(losses);
        losses.put("total", totalLoss);
        
        return losses;
    }
    
    /**
     * 计算法律条款分类损失
     */
    private double computeArticleLoss(List<Double> logits, Integer label) {
        // 根据模型输出的法律条款与训练样本中案例实际所用法律条款进行对比
        // 根据法律条款的匹配率给出(0,1)分数，模型输出的法律条款与实际所用法律条款匹配率越高，则法律条款分类损失值越低
        
        // 计算交叉熵损失
        double maxLogit = logits.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
        double sumExp = logits.stream().mapToDouble(l -> Math.exp(l)).sum();
        double predictedProb = Math.exp(maxLogit) / sumExp;
        double ceLoss = -Math.log(predictedProb + 1e-8);
        
        // 计算匹配率
        int predictedLabel = logits.indexOf(maxLogit);
        double matchRate = (predictedLabel == label) ? 1.0 : 0.0;
        
        // 根据匹配率调整损失
        return ceLoss * (1 - matchRate);
    }
    
    /**
     * 计算缺陷检测损失
     */
    private double computeDefectLoss(List<Double> logits, Double label) {
        // 模型输出案件的流程存在缺陷的概率值(0,1)，并与训练样本中案例实际的缺陷情况进行比对
        // 若存在缺陷，则Ldefect取值为1-模型输出的概率值；若不存在缺陷，则Ldefect取值为模型输出的概率值
        
        // 计算sigmoid概率
        double logit = logits.get(0);
        double probability = 1.0 / (1.0 + Math.exp(-logit));
        
        // 根据实际标签计算损失
        if (label == 1.0) {
            // 存在缺陷：1 - 模型输出的概率值
            return 1.0 - probability;
        } else {
            // 不存在缺陷：模型输出的概率值
            return probability;
        }
    }
    
    /**
     * 计算处罚金额预测损失
     */
    private double computePenaltyLoss(List<Double> pred, Double actual) {
        // 模型输出预测的处罚金额，并与训练样本中案例实际的处罚金额进行比对，输出差值的百分比误差
        
        double predicted = pred.get(0);
        double absoluteError = Math.abs(predicted - actual);
        double percentageError = absoluteError / (actual + 1e-8); // 避免除零
        
        return percentageError;
    }
    
    /**
     * 计算流程损失
     */
    private double computeProcedureLoss(Map<String, Object> outputs, Map<String, Object> labels) {
        double totalProcedureLoss = 0.0;
        int lossCount = 0;
        
        // 流程类型分类损失
        if (outputs.containsKey("procedure_class_logits") && labels.containsKey("procedure_type")) {
            List<Double> logits = (List<Double>) outputs.get("procedure_class_logits");
            Integer label = (Integer) labels.get("procedure_type");
            double classLoss = computeArticleLoss(logits, label); // 复用分类损失计算
            totalProcedureLoss += classLoss;
            lossCount++;
        }
        
        // 流程时间预测损失
        if (outputs.containsKey("procedure_time_pred") && labels.containsKey("procedure_time")) {
            List<Double> pred = (List<Double>) outputs.get("procedure_time_pred");
            Double actual = (Double) labels.get("procedure_time");
            double timeLoss = computePenaltyLoss(pred, actual); // 复用回归损失计算
            totalProcedureLoss += timeLoss;
            lossCount++;
        }
        
        // 如果有流程损失，返回平均值
        return lossCount > 0 ? totalProcedureLoss / lossCount : 0.0;
    }
    
    /**
     * 计算加权总损失
     */
    private double computeWeightedTotalLoss(Map<String, Double> losses) {
        double totalLoss = 0.0;
        
        for (Map.Entry<String, Double> entry : losses.entrySet()) {
            String lossName = entry.getKey();
            double lossValue = entry.getValue();
            
            if (weights.containsKey(lossName)) {
                double weight = weights.get(lossName);
                totalLoss += weight * lossValue;
            }
        }
        
        return totalLoss;
    }
    
    /**
     * 计算评估指标
     */
    public Map<String, Double> calculateMetrics(Map<String, Object> outputs, Map<String, Object> labels) {
        Map<String, Double> metrics = new HashMap<>();
        
        // 法律条款分类准确率
        if (outputs.containsKey("article_logits") && labels.containsKey("article")) {
            List<Double> logits = (List<Double>) outputs.get("article_logits");
            Integer actualLabel = (Integer) labels.get("article");
            
            double maxLogit = logits.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
            int predictedLabel = logits.indexOf(maxLogit);
            double accuracy = (predictedLabel == actualLabel) ? 1.0 : 0.0;
            metrics.put("article_accuracy", accuracy);
        }
        
        // 缺陷检测准确率
        if (outputs.containsKey("defect_logits") && labels.containsKey("defect")) {
            List<Double> logits = (List<Double>) outputs.get("defect_logits");
            Double actualDefect = (Double) labels.get("defect");
            
            double logit = logits.get(0);
            double probability = 1.0 / (1.0 + Math.exp(-logit));
            boolean predictedDefect = probability > 0.5;
            boolean actualDefectBool = actualDefect == 1.0;
            
            double accuracy = (predictedDefect == actualDefectBool) ? 1.0 : 0.0;
            metrics.put("defect_accuracy", accuracy);
        }
        
        // 处罚金额预测误差
        if (outputs.containsKey("penalty_pred") && labels.containsKey("penalty")) {
            List<Double> pred = (List<Double>) outputs.get("penalty_pred");
            Double actual = (Double) labels.get("penalty");
            
            double mape = calculateMape(pred.get(0), actual);
            metrics.put("penalty_mape", mape);
        }
        
        // 流程分类准确率
        if (outputs.containsKey("procedure_class_logits") && labels.containsKey("procedure_type")) {
            List<Double> logits = (List<Double>) outputs.get("procedure_class_logits");
            Integer actualType = (Integer) labels.get("procedure_type");
            
            double maxLogit = logits.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
            int predictedType = logits.indexOf(maxLogit);
            double accuracy = (predictedType == actualType) ? 1.0 : 0.0;
            metrics.put("procedure_accuracy", accuracy);
        }
        
        return metrics;
    }
    
    /**
     * 计算平均绝对百分比误差
     */
    private double calculateMape(double pred, double actual) {
        double absoluteError = Math.abs(pred - actual);
        double percentageError = absoluteError / (actual + 1e-8);
        return percentageError;
    }
    
    /**
     * 获取权重
     */
    public Map<String, Double> getWeights() {
        return new HashMap<>(weights);
    }
    
    /**
     * 设置权重
     */
    public void setWeights(Map<String, Double> weights) {
        this.weights = new HashMap<>(weights);
        
        // 验证权重和为1
        double totalWeight = this.weights.values().stream().mapToDouble(Double::doubleValue).sum();
        if (Math.abs(totalWeight - 1.0) > 1e-6) {
            throw new IllegalArgumentException("权重和必须为1，当前为" + totalWeight);
        }
    }
    
    /**
     * 测试多任务损失函数
     */
    public static void main(String[] args) {
        // 创建测试数据
        int batchSize = 4;
        int numClasses = 100;
        
        // 模拟输出
        Map<String, Object> outputs = new HashMap<>();
        List<Double> articleLogits = new ArrayList<>();
        for (int i = 0; i < numClasses; i++) {
            articleLogits.add(Math.random());
        }
        outputs.put("article_logits", articleLogits);
        
        List<Double> defectLogits = Arrays.asList(Math.random());
        outputs.put("defect_logits", defectLogits);
        
        List<Double> penaltyPred = Arrays.asList(Math.random() * 10000);
        outputs.put("penalty_pred", penaltyPred);
        
        List<Double> procedureClassLogits = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            procedureClassLogits.add(Math.random());
        }
        outputs.put("procedure_class_logits", procedureClassLogits);
        
        List<Double> procedureTimePred = Arrays.asList(Math.random() * 365);
        outputs.put("procedure_time_pred", procedureTimePred);
        
        // 模拟标签
        Map<String, Object> labels = new HashMap<>();
        labels.put("article", (int) (Math.random() * numClasses));
        labels.put("defect", Math.random() > 0.5 ? 1.0 : 0.0);
        labels.put("penalty", Math.random() * 10000);
        labels.put("procedure_type", (int) (Math.random() * 10));
        labels.put("procedure_time", Math.random() * 365);
        
        // 计算损失
        MultiTaskLoss lossCalculator = new MultiTaskLoss();
        Map<String, Double> losses = lossCalculator.calculateLosses(outputs, labels);
        Map<String, Double> metrics = lossCalculator.calculateMetrics(outputs, labels);
        
        // 打印结果
        logger.info("多任务损失计算结果:");
        for (Map.Entry<String, Double> entry : losses.entrySet()) {
            logger.info("  " + entry.getKey() + ": " + String.format("%.4f", entry.getValue()));
        }
        
        logger.info("评估指标:");
        for (Map.Entry<String, Double> entry : metrics.entrySet()) {
            logger.info("  " + entry.getKey() + ": " + String.format("%.4f", entry.getValue()));
        }
    }
} 