{"test_cases": [{"case_id": "TEST_001", "text": "某化工厂因未按规定安装污染防治设施，被环保部门处以罚款10万元。案卷包含现场检查笔录、监测报告、整改通知书、处罚决定书和送达回证。", "article_label": 15, "defect_label": 0.0, "penalty_amount": 100000.0, "procedure_type": 1, "procedure_time": 30.0, "expected_article": "《中华人民共和国环境保护法》第六十三条", "expected_defect": false, "expected_penalty_range": [80000, 120000], "expected_procedure_type": "一般行政处罚", "expected_procedure_time_range": [20, 45]}, {"case_id": "TEST_002", "text": "某企业超标排放污染物，但案卷中缺少监测报告，程序存在缺陷。处罚金额5万元。", "article_label": 23, "defect_label": 1.0, "penalty_amount": 50000.0, "procedure_type": 1, "procedure_time": 25.0, "expected_article": "《中华人民共和国水污染防治法》第八十三条", "expected_defect": true, "expected_penalty_range": [40000, 60000], "expected_procedure_type": "一般行政处罚", "expected_procedure_time_range": [15, 35]}, {"case_id": "TEST_003", "text": "某公司未取得排污许可证排放污染物，情节严重，被责令停产整治并处罚款50万元。", "article_label": 8, "defect_label": 0.0, "penalty_amount": 500000.0, "procedure_type": 5, "procedure_time": 60.0, "expected_article": "《中华人民共和国环境保护法》第六十一条", "expected_defect": false, "expected_penalty_range": [400000, 600000], "expected_procedure_type": "限产停产", "expected_procedure_time_range": [45, 75]}, {"case_id": "TEST_004", "text": "某工厂偷排废水，造成环境污染，案卷中证据链完整，包括现场检查笔录、监测报告、整改通知书、处罚决定书和送达回证。", "article_label": 12, "defect_label": 0.0, "penalty_amount": 200000.0, "procedure_type": 1, "procedure_time": 40.0, "expected_article": "《中华人民共和国水污染防治法》第八十三条", "expected_defect": false, "expected_penalty_range": [160000, 240000], "expected_procedure_type": "一般行政处罚", "expected_procedure_time_range": [30, 50]}, {"case_id": "TEST_005", "text": "某企业未按规定设置危险废物识别标志，案卷中缺少整改通知书，程序存在缺陷。处罚金额3万元。", "article_label": 31, "defect_label": 1.0, "penalty_amount": 30000.0, "procedure_type": 1, "procedure_time": 20.0, "expected_article": "《中华人民共和国固体废物污染环境防治法》第一百一十二条", "expected_defect": true, "expected_penalty_range": [24000, 36000], "expected_procedure_type": "一般行政处罚", "expected_procedure_time_range": [15, 30]}], "evaluation_metrics": {"article_accuracy_threshold": 0.85, "defect_accuracy_threshold": 0.9, "penalty_mape_threshold": 0.15, "procedure_accuracy_threshold": 0.8, "procedure_time_mape_threshold": 0.2}, "test_scenarios": {"scenario_1": {"name": "法律条款分类测试", "description": "测试模型对法律条款的准确分类能力", "test_cases": ["TEST_001", "TEST_003", "TEST_004"]}, "scenario_2": {"name": "缺陷检测测试", "description": "测试模型对案卷程序缺陷的检测能力", "test_cases": ["TEST_002", "TEST_005"]}, "scenario_3": {"name": "处罚金额预测测试", "description": "测试模型对处罚金额的预测准确性", "test_cases": ["TEST_001", "TEST_002", "TEST_003", "TEST_004", "TEST_005"]}, "scenario_4": {"name": "流程类型分类测试", "description": "测试模型对流程类型的分类准确性", "test_cases": ["TEST_001", "TEST_003"]}, "scenario_5": {"name": "流程时间预测测试", "description": "测试模型对流程时间的预测准确性", "test_cases": ["TEST_001", "TEST_002", "TEST_003", "TEST_004", "TEST_005"]}}}