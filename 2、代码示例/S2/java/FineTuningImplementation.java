package com.ecoenvironment.review.s2;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * S2评查专项微调核心实现
 * 基于S1训练的初始评查模型，进行多任务学习微调
 */
public class FineTuningImplementation {
    
    private static final Logger logger = Logger.getLogger(FineTuningImplementation.class.getName());
    
    /**
     * 评查专项微调配置类
     */
    public static class FineTuningConfig {
        private String modelName = "chatglm3-6b";
        private double learningRate = 2e-5;
        private int batchSize = 8;
        private int maxEpochs = 10;
        private double weightArticle = 0.4;
        private double weightDefect = 0.3;
        private double weightPenalty = 0.2;
        private double weightProcedure = 0.1;
        private double targetArticleLoss = 0.05;
        private double targetDefectLoss = 0.1;
        private double targetPenaltyLoss = 0.15;
        private double targetProcedureLoss = 0.1;
        private int earlyStoppingPatience = 5;
        private double minImprovement = 0.05;
        
        // Getters and Setters
        public String getModelName() { return modelName; }
        public void setModelName(String modelName) { this.modelName = modelName; }
        
        public double getLearningRate() { return learningRate; }
        public void setLearningRate(double learningRate) { this.learningRate = learningRate; }
        
        public int getBatchSize() { return batchSize; }
        public void setBatchSize(int batchSize) { this.batchSize = batchSize; }
        
        public int getMaxEpochs() { return maxEpochs; }
        public void setMaxEpochs(int maxEpochs) { this.maxEpochs = maxEpochs; }
        
        public double getWeightArticle() { return weightArticle; }
        public void setWeightArticle(double weightArticle) { this.weightArticle = weightArticle; }
        
        public double getWeightDefect() { return weightDefect; }
        public void setWeightDefect(double weightDefect) { this.weightDefect = weightDefect; }
        
        public double getWeightPenalty() { return weightPenalty; }
        public void setWeightPenalty(double weightPenalty) { this.weightPenalty = weightPenalty; }
        
        public double getWeightProcedure() { return weightProcedure; }
        public void setWeightProcedure(double weightProcedure) { this.weightProcedure = weightProcedure; }
        
        public double getTargetArticleLoss() { return targetArticleLoss; }
        public void setTargetArticleLoss(double targetArticleLoss) { this.targetArticleLoss = targetArticleLoss; }
        
        public double getTargetDefectLoss() { return targetDefectLoss; }
        public void setTargetDefectLoss(double targetDefectLoss) { this.targetDefectLoss = targetDefectLoss; }
        
        public double getTargetPenaltyLoss() { return targetPenaltyLoss; }
        public void setTargetPenaltyLoss(double targetPenaltyLoss) { this.targetPenaltyLoss = targetPenaltyLoss; }
        
        public double getTargetProcedureLoss() { return targetProcedureLoss; }
        public void setTargetProcedureLoss(double targetProcedureLoss) { this.targetProcedureLoss = targetProcedureLoss; }
        
        public int getEarlyStoppingPatience() { return earlyStoppingPatience; }
        public void setEarlyStoppingPatience(int earlyStoppingPatience) { this.earlyStoppingPatience = earlyStoppingPatience; }
        
        public double getMinImprovement() { return minImprovement; }
        public void setMinImprovement(double minImprovement) { this.minImprovement = minImprovement; }
    }
    
    /**
     * 评查案例数据类
     */
    public static class ReviewCase {
        private String text;
        private int articleLabel;
        private double defectLabel;
        private double penaltyAmount;
        private int procedureType;
        private double procedureTime;
        
        public ReviewCase(String text, int articleLabel, double defectLabel, 
                         double penaltyAmount, int procedureType, double procedureTime) {
            this.text = text;
            this.articleLabel = articleLabel;
            this.defectLabel = defectLabel;
            this.penaltyAmount = penaltyAmount;
            this.procedureType = procedureType;
            this.procedureTime = procedureTime;
        }
        
        // Getters
        public String getText() { return text; }
        public int getArticleLabel() { return articleLabel; }
        public double getDefectLabel() { return defectLabel; }
        public double getPenaltyAmount() { return penaltyAmount; }
        public int getProcedureType() { return procedureType; }
        public double getProcedureTime() { return procedureTime; }
    }
    
    /**
     * 多任务学习模型
     */
    public static class MultiTaskModel {
        private FineTuningConfig config;
        private Map<String, Object> baseModel;
        private Map<String, Object> tokenizer;
        
        public MultiTaskModel(FineTuningConfig config) {
            this.config = config;
            initializeModel();
        }
        
        private void initializeModel() {
            // 初始化基础模型和分词器
            logger.info("初始化多任务学习模型: " + config.getModelName());
            this.baseModel = new ConcurrentHashMap<>();
            this.tokenizer = new ConcurrentHashMap<>();
        }
        
        /**
         * 前向传播
         */
        public Map<String, Object> forward(List<String> inputIds, List<Integer> attentionMask, 
                                         Map<String, Object> labels) {
            Map<String, Object> outputs = new HashMap<>();
            
            // 模拟基础模型前向传播
            List<Double> pooledOutput = simulateBaseModelForward(inputIds, attentionMask);
            
            // 多任务输出
            outputs.put("article_logits", simulateArticleClassification(pooledOutput));
            outputs.put("defect_logits", simulateDefectDetection(pooledOutput));
            outputs.put("penalty_pred", simulatePenaltyPrediction(pooledOutput));
            outputs.put("procedure_class_logits", simulateProcedureClassification(pooledOutput));
            outputs.put("procedure_time_pred", simulateProcedureTimePrediction(pooledOutput));
            
            if (labels != null) {
                // 计算多任务损失
                Map<String, Double> losses = computeLosses(outputs, labels);
                outputs.put("losses", losses);
                outputs.put("total_loss", computeTotalLoss(losses));
            }
            
            return outputs;
        }
        
        private List<Double> simulateBaseModelForward(List<String> inputIds, List<Integer> attentionMask) {
            // 模拟基础模型前向传播
            List<Double> pooledOutput = new ArrayList<>();
            for (int i = 0; i < 768; i++) { // 假设隐藏层维度为768
                pooledOutput.add(Math.random());
            }
            return pooledOutput;
        }
        
        private List<Double> simulateArticleClassification(List<Double> pooledOutput) {
            // 模拟法律条款分类
            List<Double> logits = new ArrayList<>();
            for (int i = 0; i < 100; i++) { // 100个法律条款类别
                logits.add(Math.random());
            }
            return logits;
        }
        
        private List<Double> simulateDefectDetection(List<Double> pooledOutput) {
            // 模拟缺陷检测
            return Arrays.asList(Math.random());
        }
        
        private List<Double> simulatePenaltyPrediction(List<Double> pooledOutput) {
            // 模拟处罚金额预测
            return Arrays.asList(Math.random() * 10000);
        }
        
        private List<Double> simulateProcedureClassification(List<Double> pooledOutput) {
            // 模拟流程类型分类
            List<Double> logits = new ArrayList<>();
            for (int i = 0; i < 10; i++) { // 10个流程类型
                logits.add(Math.random());
            }
            return logits;
        }
        
        private List<Double> simulateProcedureTimePrediction(List<Double> pooledOutput) {
            // 模拟流程时间预测
            return Arrays.asList(Math.random() * 365);
        }
        
        private Map<String, Double> computeLosses(Map<String, Object> outputs, Map<String, Object> labels) {
            Map<String, Double> losses = new HashMap<>();
            
            // 法律条款分类损失
            if (outputs.containsKey("article_logits") && labels.containsKey("article")) {
                losses.put("article", computeArticleLoss(
                    (List<Double>) outputs.get("article_logits"), 
                    (Integer) labels.get("article")
                ));
            }
            
            // 缺陷检测损失
            if (outputs.containsKey("defect_logits") && labels.containsKey("defect")) {
                losses.put("defect", computeDefectLoss(
                    (List<Double>) outputs.get("defect_logits"), 
                    (Double) labels.get("defect")
                ));
            }
            
            // 处罚金额预测损失
            if (outputs.containsKey("penalty_pred") && labels.containsKey("penalty")) {
                losses.put("penalty", computePenaltyLoss(
                    (List<Double>) outputs.get("penalty_pred"), 
                    (Double) labels.get("penalty")
                ));
            }
            
            // 流程损失
            if ((outputs.containsKey("procedure_class_logits") || outputs.containsKey("procedure_time_pred")) &&
                (labels.containsKey("procedure_type") || labels.containsKey("procedure_time"))) {
                losses.put("procedure", computeProcedureLoss(outputs, labels));
            }
            
            return losses;
        }
        
        private double computeArticleLoss(List<Double> logits, Integer label) {
            // 计算法律条款分类损失
            double maxLogit = logits.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
            double predictedProb = Math.exp(maxLogit) / logits.stream().mapToDouble(l -> Math.exp(l)).sum();
            return -Math.log(predictedProb + 1e-8);
        }
        
        private double computeDefectLoss(List<Double> logits, Double label) {
            // 计算缺陷检测损失
            double probability = 1.0 / (1.0 + Math.exp(-logits.get(0)));
            return label == 1.0 ? 1.0 - probability : probability;
        }
        
        private double computePenaltyLoss(List<Double> pred, Double actual) {
            // 计算处罚金额预测损失
            double predicted = pred.get(0);
            return Math.abs(predicted - actual) / (actual + 1e-8);
        }
        
        private double computeProcedureLoss(Map<String, Object> outputs, Map<String, Object> labels) {
            // 计算流程损失
            double totalLoss = 0.0;
            int lossCount = 0;
            
            if (outputs.containsKey("procedure_class_logits") && labels.containsKey("procedure_type")) {
                List<Double> logits = (List<Double>) outputs.get("procedure_class_logits");
                Integer label = (Integer) labels.get("procedure_type");
                totalLoss += computeArticleLoss(logits, label); // 复用分类损失计算
                lossCount++;
            }
            
            if (outputs.containsKey("procedure_time_pred") && labels.containsKey("procedure_time")) {
                List<Double> pred = (List<Double>) outputs.get("procedure_time_pred");
                Double actual = (Double) labels.get("procedure_time");
                totalLoss += computePenaltyLoss(pred, actual); // 复用回归损失计算
                lossCount++;
            }
            
            return lossCount > 0 ? totalLoss / lossCount : 0.0;
        }
        
        private double computeTotalLoss(Map<String, Double> losses) {
            // 计算加权总损失
            return config.getWeightArticle() * losses.getOrDefault("article", 0.0) +
                   config.getWeightDefect() * losses.getOrDefault("defect", 0.0) +
                   config.getWeightPenalty() * losses.getOrDefault("penalty", 0.0) +
                   config.getWeightProcedure() * losses.getOrDefault("procedure", 0.0);
        }
    }
    
    /**
     * 评查案例训练器
     */
    public static class ReviewCaseTrainer {
        private MultiTaskModel model;
        private FineTuningConfig config;
        private double bestLoss = Double.MAX_VALUE;
        private int patienceCounter = 0;
        
        public ReviewCaseTrainer(MultiTaskModel model, FineTuningConfig config) {
            this.model = model;
            this.config = config;
        }
        
        /**
         * 训练模型
         */
        public void train(List<ReviewCase> trainData, List<ReviewCase> valData) {
            logger.info("开始训练评查专项微调模型");
            
            for (int epoch = 0; epoch < config.getMaxEpochs(); epoch++) {
                logger.info("Epoch " + (epoch + 1) + "/" + config.getMaxEpochs());
                
                // 训练阶段
                Map<String, Double> trainLosses = trainEpoch(trainData);
                
                // 验证阶段
                Map<String, Double> valLosses = validateEpoch(valData);
                
                // 打印训练信息
                logEpochInfo(epoch, trainLosses, valLosses);
                
                // 早停检查
                if (shouldStopEarly(valLosses)) {
                    logger.info("触发早停机制，停止训练");
                    break;
                }
            }
            
            logger.info("训练完成");
        }
        
        private Map<String, Double> trainEpoch(List<ReviewCase> trainData) {
            // 模拟训练一个epoch
            Map<String, Double> losses = new HashMap<>();
            losses.put("article", Math.random() * 0.1);
            losses.put("defect", Math.random() * 0.1);
            losses.put("penalty", Math.random() * 0.2);
            losses.put("procedure_class", Math.random() * 0.1);
            losses.put("procedure_time", Math.random() * 0.1);
            losses.put("total_loss", Math.random() * 0.15);
            return losses;
        }
        
        private Map<String, Double> validateEpoch(List<ReviewCase> valData) {
            // 模拟验证一个epoch
            Map<String, Double> losses = new HashMap<>();
            losses.put("article", Math.random() * 0.08);
            losses.put("defect", Math.random() * 0.08);
            losses.put("penalty", Math.random() * 0.15);
            losses.put("procedure_class", Math.random() * 0.08);
            losses.put("procedure_time", Math.random() * 0.08);
            losses.put("total_loss", Math.random() * 0.12);
            return losses;
        }
        
        private void logEpochInfo(int epoch, Map<String, Double> trainLosses, Map<String, Double> valLosses) {
            logger.info("Epoch " + (epoch + 1) + " 训练损失: " + String.format("%.4f", trainLosses.get("total_loss")));
            logger.info("Epoch " + (epoch + 1) + " 验证损失: " + String.format("%.4f", valLosses.get("total_loss")));
            logger.info("  法律条款分类损失: " + String.format("%.4f", valLosses.get("article")));
            logger.info("  缺陷检测损失: " + String.format("%.4f", valLosses.get("defect")));
            logger.info("  处罚金额预测损失: " + String.format("%.4f", valLosses.get("penalty")));
            logger.info("  流程分类损失: " + String.format("%.4f", valLosses.get("procedure_class")));
            logger.info("  流程时间预测损失: " + String.format("%.4f", valLosses.get("procedure_time")));
        }
        
        private boolean shouldStopEarly(Map<String, Double> valLosses) {
            double currentLoss = valLosses.get("total_loss");
            
            // 检查是否达到目标值
            if (valLosses.get("article") <= config.getTargetArticleLoss() &&
                valLosses.get("defect") <= config.getTargetDefectLoss() &&
                valLosses.get("penalty") <= config.getTargetPenaltyLoss() &&
                valLosses.get("procedure_class") + valLosses.get("procedure_time") <= config.getTargetProcedureLoss()) {
                logger.info("达到目标损失值，停止训练");
                return true;
            }
            
            // 检查是否有改善
            if (currentLoss < bestLoss - config.getMinImprovement()) {
                bestLoss = currentLoss;
                patienceCounter = 0;
            } else {
                patienceCounter++;
            }
            
            // 检查是否超过耐心值
            if (patienceCounter >= config.getEarlyStoppingPatience()) {
                logger.info("连续" + config.getEarlyStoppingPatience() + "轮无改善，触发早停");
                return true;
            }
            
            return false;
        }
    }
    
    /**
     * 主函数
     */
    public static void main(String[] args) {
        // 配置参数
        FineTuningConfig config = new FineTuningConfig();
        config.setModelName("chatglm3-6b");
        config.setLearningRate(2e-5);
        config.setBatchSize(8);
        config.setMaxEpochs(10);
        config.setWeightArticle(0.4);
        config.setWeightDefect(0.3);
        config.setWeightPenalty(0.2);
        config.setWeightProcedure(0.1);
        
        // 初始化模型
        MultiTaskModel model = new MultiTaskModel(config);
        
        // 初始化训练器
        ReviewCaseTrainer trainer = new ReviewCaseTrainer(model, config);
        
        // 创建模拟训练数据
        List<ReviewCase> trainData = createMockData(1000);
        List<ReviewCase> valData = createMockData(200);
        
        // 训练模型
        trainer.train(trainData, valData);
        
        logger.info("S2评查专项微调训练完成");
    }
    
    private static List<ReviewCase> createMockData(int size) {
        List<ReviewCase> data = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            data.add(new ReviewCase(
                "模拟案卷文本内容" + i,
                (int) (Math.random() * 100),
                Math.random() > 0.5 ? 1.0 : 0.0,
                Math.random() * 10000,
                (int) (Math.random() * 10),
                Math.random() * 365
            ));
        }
        return data;
    }
} 