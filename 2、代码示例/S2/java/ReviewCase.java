package com.ecoenvironment.review.s2;

import java.util.Date;

/**
 * 评查案例类
 * 用于表示生态环境行政处罚案卷评查的训练和测试案例
 * 
 * <AUTHOR> AI Team
 * @version 1.0.0
 * @since 2024-01-01
 */
public class ReviewCase {
    
    private String caseId;
    private String text;
    private int articleLabel;
    private double defectLabel;
    private double penaltyAmount;
    private int procedureType;
    private double procedureTime;
    private String articleName;
    private String defectDescription;
    private String procedureTypeName;
    private Date createdTime;
    private String reviewerId;
    private double reviewScore;
    private String status;
    private Date updatedTime;
    
    /**
     * 构造函数
     */
    public ReviewCase() {
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.status = "ACTIVE";
    }
    
    /**
     * 构造函数
     */
    public ReviewCase(String text, int articleLabel, double defectLabel, 
                     double penaltyAmount, int procedureType, double procedureTime) {
        this();
        this.text = text;
        this.articleLabel = articleLabel;
        this.defectLabel = defectLabel;
        this.penaltyAmount = penaltyAmount;
        this.procedureType = procedureType;
        this.procedureTime = procedureTime;
    }
    
    /**
     * 构造函数
     */
    public ReviewCase(String caseId, String text, int articleLabel, double defectLabel, 
                     double penaltyAmount, int procedureType, double procedureTime) {
        this(text, articleLabel, defectLabel, penaltyAmount, procedureType, procedureTime);
        this.caseId = caseId;
    }
    
    // Getters and Setters
    public String getCaseId() { return caseId; }
    public void setCaseId(String caseId) { this.caseId = caseId; }
    
    public String getText() { return text; }
    public void setText(String text) { this.text = text; }
    
    public int getArticleLabel() { return articleLabel; }
    public void setArticleLabel(int articleLabel) { this.articleLabel = articleLabel; }
    
    public double getDefectLabel() { return defectLabel; }
    public void setDefectLabel(double defectLabel) { this.defectLabel = defectLabel; }
    
    public double getPenaltyAmount() { return penaltyAmount; }
    public void setPenaltyAmount(double penaltyAmount) { this.penaltyAmount = penaltyAmount; }
    
    public int getProcedureType() { return procedureType; }
    public void setProcedureType(int procedureType) { this.procedureType = procedureType; }
    
    public double getProcedureTime() { return procedureTime; }
    public void setProcedureTime(double procedureTime) { this.procedureTime = procedureTime; }
    
    public String getArticleName() { return articleName; }
    public void setArticleName(String articleName) { this.articleName = articleName; }
    
    public String getDefectDescription() { return defectDescription; }
    public void setDefectDescription(String defectDescription) { this.defectDescription = defectDescription; }
    
    public String getProcedureTypeName() { return procedureTypeName; }
    public void setProcedureTypeName(String procedureTypeName) { this.procedureTypeName = procedureTypeName; }
    
    public Date getCreatedTime() { return createdTime; }
    public void setCreatedTime(Date createdTime) { this.createdTime = createdTime; }
    
    public String getReviewerId() { return reviewerId; }
    public void setReviewerId(String reviewerId) { this.reviewerId = reviewerId; }
    
    public double getReviewScore() { return reviewScore; }
    public void setReviewScore(double reviewScore) { this.reviewScore = reviewScore; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public Date getUpdatedTime() { return updatedTime; }
    public void setUpdatedTime(Date updatedTime) { this.updatedTime = updatedTime; }
    
    @Override
    public String toString() {
        return "ReviewCase{" +
                "caseId='" + caseId + '\'' +
                ", articleLabel=" + articleLabel +
                ", defectLabel=" + defectLabel +
                ", penaltyAmount=" + penaltyAmount +
                ", procedureType=" + procedureType +
                ", procedureTime=" + procedureTime +
                ", reviewScore=" + reviewScore +
                ", status='" + status + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        ReviewCase that = (ReviewCase) o;
        
        return caseId != null ? caseId.equals(that.caseId) : that.caseId == null;
    }
    
    @Override
    public int hashCode() {
        return caseId != null ? caseId.hashCode() : 0;
    }
} 