package com.ecoenvironment.review.s2;

import java.util.*;

/**
 * 微调配置类
 * 用于配置生态环境行政处罚案卷评查模型的微调参数
 * 
 * <AUTHOR> AI Team
 * @version 1.0.0
 * @since 2024-01-01
 */
public class FineTuningConfig {
    
    // 基础配置
    private String modelName = "chatglm3-6b";
    private double learningRate = 2e-5;
    private int batchSize = 8;
    private int maxEpochs = 10;
    private int warmupSteps = 100;
    private double weightDecay = 0.01;
    private int gradientAccumulationSteps = 4;
    private double maxGradNorm = 1.0;
    private String lrSchedulerType = "cosine";
    private int saveSteps = 500;
    private int evalSteps = 500;
    private int loggingSteps = 100;
    private int saveTotalLimit = 3;
    private boolean loadBestModelAtEnd = true;
    private String metricForBestModel = "eval_loss";
    private boolean greaterIsBetter = false;
    
    // 多任务配置
    private double weightArticle = 0.4;
    private double weightDefect = 0.3;
    private double weightPenalty = 0.2;
    private double weightProcedure = 0.1;
    
    // 目标损失值
    private double targetArticleLoss = 0.05;
    private double targetDefectLoss = 0.1;
    private double targetPenaltyLoss = 0.15;
    private double targetProcedureLoss = 0.1;
    
    // 早停配置
    private int earlyStoppingPatience = 5;
    private double minImprovement = 0.05;
    
    // 数据配置
    private String trainDataPath;
    private String valDataPath;
    private String testDataPath;
    private int maxLength = 2048;
    private boolean useDataParallel = false;
    
    /**
     * 构造函数
     */
    public FineTuningConfig() {
    }
    
    /**
     * 构造函数
     */
    public FineTuningConfig(String modelName) {
        this.modelName = modelName;
    }
    
    // Getters and Setters
    public String getModelName() { return modelName; }
    public void setModelName(String modelName) { this.modelName = modelName; }
    
    public double getLearningRate() { return learningRate; }
    public void setLearningRate(double learningRate) { this.learningRate = learningRate; }
    
    public int getBatchSize() { return batchSize; }
    public void setBatchSize(int batchSize) { this.batchSize = batchSize; }
    
    public int getMaxEpochs() { return maxEpochs; }
    public void setMaxEpochs(int maxEpochs) { this.maxEpochs = maxEpochs; }
    
    public int getWarmupSteps() { return warmupSteps; }
    public void setWarmupSteps(int warmupSteps) { this.warmupSteps = warmupSteps; }
    
    public double getWeightDecay() { return weightDecay; }
    public void setWeightDecay(double weightDecay) { this.weightDecay = weightDecay; }
    
    public int getGradientAccumulationSteps() { return gradientAccumulationSteps; }
    public void setGradientAccumulationSteps(int gradientAccumulationSteps) { this.gradientAccumulationSteps = gradientAccumulationSteps; }
    
    public double getMaxGradNorm() { return maxGradNorm; }
    public void setMaxGradNorm(double maxGradNorm) { this.maxGradNorm = maxGradNorm; }
    
    public String getLrSchedulerType() { return lrSchedulerType; }
    public void setLrSchedulerType(String lrSchedulerType) { this.lrSchedulerType = lrSchedulerType; }
    
    public int getSaveSteps() { return saveSteps; }
    public void setSaveSteps(int saveSteps) { this.saveSteps = saveSteps; }
    
    public int getEvalSteps() { return evalSteps; }
    public void setEvalSteps(int evalSteps) { this.evalSteps = evalSteps; }
    
    public int getLoggingSteps() { return loggingSteps; }
    public void setLoggingSteps(int loggingSteps) { this.loggingSteps = loggingSteps; }
    
    public int getSaveTotalLimit() { return saveTotalLimit; }
    public void setSaveTotalLimit(int saveTotalLimit) { this.saveTotalLimit = saveTotalLimit; }
    
    public boolean isLoadBestModelAtEnd() { return loadBestModelAtEnd; }
    public void setLoadBestModelAtEnd(boolean loadBestModelAtEnd) { this.loadBestModelAtEnd = loadBestModelAtEnd; }
    
    public String getMetricForBestModel() { return metricForBestModel; }
    public void setMetricForBestModel(String metricForBestModel) { this.metricForBestModel = metricForBestModel; }
    
    public boolean isGreaterIsBetter() { return greaterIsBetter; }
    public void setGreaterIsBetter(boolean greaterIsBetter) { this.greaterIsBetter = greaterIsBetter; }
    
    public double getWeightArticle() { return weightArticle; }
    public void setWeightArticle(double weightArticle) { this.weightArticle = weightArticle; }
    
    public double getWeightDefect() { return weightDefect; }
    public void setWeightDefect(double weightDefect) { this.weightDefect = weightDefect; }
    
    public double getWeightPenalty() { return weightPenalty; }
    public void setWeightPenalty(double weightPenalty) { this.weightPenalty = weightPenalty; }
    
    public double getWeightProcedure() { return weightProcedure; }
    public void setWeightProcedure(double weightProcedure) { this.weightProcedure = weightProcedure; }
    
    public double getTargetArticleLoss() { return targetArticleLoss; }
    public void setTargetArticleLoss(double targetArticleLoss) { this.targetArticleLoss = targetArticleLoss; }
    
    public double getTargetDefectLoss() { return targetDefectLoss; }
    public void setTargetDefectLoss(double targetDefectLoss) { this.targetDefectLoss = targetDefectLoss; }
    
    public double getTargetPenaltyLoss() { return targetPenaltyLoss; }
    public void setTargetPenaltyLoss(double targetPenaltyLoss) { this.targetPenaltyLoss = targetPenaltyLoss; }
    
    public double getTargetProcedureLoss() { return targetProcedureLoss; }
    public void setTargetProcedureLoss(double targetProcedureLoss) { this.targetProcedureLoss = targetProcedureLoss; }
    
    public int getEarlyStoppingPatience() { return earlyStoppingPatience; }
    public void setEarlyStoppingPatience(int earlyStoppingPatience) { this.earlyStoppingPatience = earlyStoppingPatience; }
    
    public double getMinImprovement() { return minImprovement; }
    public void setMinImprovement(double minImprovement) { this.minImprovement = minImprovement; }
    
    public String getTrainDataPath() { return trainDataPath; }
    public void setTrainDataPath(String trainDataPath) { this.trainDataPath = trainDataPath; }
    
    public String getValDataPath() { return valDataPath; }
    public void setValDataPath(String valDataPath) { this.valDataPath = valDataPath; }
    
    public String getTestDataPath() { return testDataPath; }
    public void setTestDataPath(String testDataPath) { this.testDataPath = testDataPath; }
    
    public int getMaxLength() { return maxLength; }
    public void setMaxLength(int maxLength) { this.maxLength = maxLength; }
    
    public boolean isUseDataParallel() { return useDataParallel; }
    public void setUseDataParallel(boolean useDataParallel) { this.useDataParallel = useDataParallel; }
    
    /**
     * 验证配置
     */
    public void validate() {
        List<String> errors = new ArrayList<>();
        
        // 验证权重和
        double totalWeight = weightArticle + weightDefect + weightPenalty + weightProcedure;
        if (Math.abs(totalWeight - 1.0) > 1e-6) {
            errors.add("权重和必须为1，当前为" + totalWeight);
        }
        
        // 验证学习率
        if (learningRate <= 0) {
            errors.add("学习率必须大于0");
        }
        
        // 验证批次大小
        if (batchSize <= 0) {
            errors.add("批次大小必须大于0");
        }
        
        // 验证最大轮数
        if (maxEpochs <= 0) {
            errors.add("最大轮数必须大于0");
        }
        
        // 验证目标损失值
        if (targetArticleLoss < 0 || targetDefectLoss < 0 || targetPenaltyLoss < 0 || targetProcedureLoss < 0) {
            errors.add("目标损失值不能为负数");
        }
        
        // 验证早停配置
        if (earlyStoppingPatience <= 0) {
            errors.add("早停耐心值必须大于0");
        }
        
        if (!errors.isEmpty()) {
            throw new IllegalArgumentException("配置验证失败: " + String.join(", ", errors));
        }
    }
    
    /**
     * 获取权重映射
     */
    public Map<String, Double> getWeights() {
        Map<String, Double> weights = new HashMap<>();
        weights.put("article", weightArticle);
        weights.put("defect", weightDefect);
        weights.put("penalty", weightPenalty);
        weights.put("procedure", weightProcedure);
        return weights;
    }
    
    /**
     * 设置权重映射
     */
    public void setWeights(Map<String, Double> weights) {
        this.weightArticle = weights.getOrDefault("article", 0.4);
        this.weightDefect = weights.getOrDefault("defect", 0.3);
        this.weightPenalty = weights.getOrDefault("penalty", 0.2);
        this.weightProcedure = weights.getOrDefault("procedure", 0.1);
    }
    
    /**
     * 获取目标损失映射
     */
    public Map<String, Double> getTargetLosses() {
        Map<String, Double> targetLosses = new HashMap<>();
        targetLosses.put("article", targetArticleLoss);
        targetLosses.put("defect", targetDefectLoss);
        targetLosses.put("penalty", targetPenaltyLoss);
        targetLosses.put("procedure", targetProcedureLoss);
        return targetLosses;
    }
    
    /**
     * 设置目标损失映射
     */
    public void setTargetLosses(Map<String, Double> targetLosses) {
        this.targetArticleLoss = targetLosses.getOrDefault("article", 0.05);
        this.targetDefectLoss = targetLosses.getOrDefault("defect", 0.1);
        this.targetPenaltyLoss = targetLosses.getOrDefault("penalty", 0.15);
        this.targetProcedureLoss = targetLosses.getOrDefault("procedure", 0.1);
    }
    
    /**
     * 转换为JSON格式
     */
    public String toJson() {
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"model_name\":\"").append(modelName).append("\",");
        json.append("\"learning_rate\":").append(learningRate).append(",");
        json.append("\"batch_size\":").append(batchSize).append(",");
        json.append("\"max_epochs\":").append(maxEpochs).append(",");
        json.append("\"weight_article\":").append(weightArticle).append(",");
        json.append("\"weight_defect\":").append(weightDefect).append(",");
        json.append("\"weight_penalty\":").append(weightPenalty).append(",");
        json.append("\"weight_procedure\":").append(weightProcedure).append(",");
        json.append("\"target_article_loss\":").append(targetArticleLoss).append(",");
        json.append("\"target_defect_loss\":").append(targetDefectLoss).append(",");
        json.append("\"target_penalty_loss\":").append(targetPenaltyLoss).append(",");
        json.append("\"target_procedure_loss\":").append(targetProcedureLoss).append(",");
        json.append("\"early_stopping_patience\":").append(earlyStoppingPatience).append(",");
        json.append("\"min_improvement\":").append(minImprovement);
        json.append("}");
        return json.toString();
    }
    
    @Override
    public String toString() {
        return "FineTuningConfig{" +
                "modelName='" + modelName + '\'' +
                ", learningRate=" + learningRate +
                ", batchSize=" + batchSize +
                ", maxEpochs=" + maxEpochs +
                ", weightArticle=" + weightArticle +
                ", weightDefect=" + weightDefect +
                ", weightPenalty=" + weightPenalty +
                ", weightProcedure=" + weightProcedure +
                ", targetArticleLoss=" + targetArticleLoss +
                ", targetDefectLoss=" + targetDefectLoss +
                ", targetPenaltyLoss=" + targetPenaltyLoss +
                ", targetProcedureLoss=" + targetProcedureLoss +
                ", earlyStoppingPatience=" + earlyStoppingPatience +
                ", minImprovement=" + minImprovement +
                '}';
    }
} 