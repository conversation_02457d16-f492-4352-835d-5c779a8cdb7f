# 生态环境行政处罚案卷评查系统 - 模型配置详细报告

## 1. 系统概述

### 1.1 项目背景
生态环境行政处罚案卷评查系统是一个基于深度学习的智能法律文书分析系统，通过多任务学习技术对生态环境行政处罚案卷进行自动化评查。系统采用ChatGLM3-6B作为基础模型，通过领域自适应预训练和评查专项微调两个阶段，实现对案卷的智能分析和评估。

### 1.2 模型架构设计
系统采用分层架构设计，包括：
- **基础模型层**：ChatGLM3-6B预训练模型
- **领域适应层**：Prefix Tuning领域自适应预训练
- **任务微调层**：多任务学习评查专项微调
- **配置管理层**：统一的配置管理和验证机制

### 1.3 配置管理目标
- 提供灵活的模型参数配置
- 支持多环境部署和配置切换
- 确保配置的安全性和一致性
- 实现配置的热更新和版本管理

## 2. 模型配置文件详细设计

### 2.1 model-config.json（模型参数配置）

#### 2.1.1 配置结构设计
```json
{
  "model_config": {
    "model_name": "chatglm3-6b",
    "model_type": "transformer",
    "base_model_path": "/models/chatglm3-6b",
    "vocab_size": 65024,
    "hidden_size": 4096,
    "num_attention_heads": 32,
    "num_hidden_layers": 28,
    "intermediate_size": 13696,
    "max_position_embeddings": 2048,
    "layer_norm_eps": 1e-5,
    "dropout": 0.1,
    "attention_dropout": 0.1,
    "activation_function": "gelu",
    "pad_token_id": 0,
    "bos_token_id": 1,
    "eos_token_id": 2,
    "tie_word_embeddings": false,
    "torch_dtype": "float16",
    "transformers_version": "4.35.0",
    "use_cache": true,
    "architectures": ["ChatGLMModel"]
  },
  "tokenizer_config": {
    "tokenizer_type": "ChatGLMTokenizer",
    "vocab_file": "/models/chatglm3-6b/tokenizer.model",
    "model_max_length": 2048,
    "padding_side": "left",
    "truncation_side": "right",
    "clean_up_tokenization_spaces": true,
    "use_fast": false
  },
  "preprocessing_config": {
    "max_length": 1024,
    "truncation": true,
    "padding": "max_length",
    "return_tensors": "pt",
    "add_special_tokens": true
  }
}
```

#### 2.1.2 配置参数详细说明

| 配置项 | 数据类型 | 默认值 | 说明 | 取值范围 |
|--------|----------|--------|------|----------|
| model_name | String | "chatglm3-6b" | 模型名称 | 任意字符串 |
| model_type | String | "transformer" | 模型类型 | transformer, gpt, bert |
| base_model_path | String | "/models/chatglm3-6b" | 基础模型路径 | 有效文件路径 |
| vocab_size | Integer | 65024 | 词汇表大小 | 1000-100000 |
| hidden_size | Integer | 4096 | 隐藏层大小 | 512-8192 |
| num_attention_heads | Integer | 32 | 注意力头数 | 8-64 |
| num_hidden_layers | Integer | 28 | 隐藏层数 | 6-48 |
| intermediate_size | Integer | 13696 | 中间层大小 | 2048-16384 |
| max_position_embeddings | Integer | 2048 | 最大位置编码 | 512-4096 |
| layer_norm_eps | Float | 1e-5 | 层归一化epsilon | 1e-6-1e-4 |
| dropout | Float | 0.1 | Dropout率 | 0.0-0.5 |
| attention_dropout | Float | 0.1 | 注意力Dropout率 | 0.0-0.5 |
| activation_function | String | "gelu" | 激活函数 | gelu, relu, swish |
| pad_token_id | Integer | 0 | 填充token ID | 0-65535 |
| bos_token_id | Integer | 1 | 开始token ID | 0-65535 |
| eos_token_id | Integer | 2 | 结束token ID | 0-65535 |
| tie_word_embeddings | Boolean | false | 是否绑定词嵌入 | true, false |
| torch_dtype | String | "float16" | 数据类型 | float16, float32, bfloat16 |
| transformers_version | String | "4.35.0" | Transformers版本 | 4.0.0+ |
| use_cache | Boolean | true | 是否使用缓存 | true, false |
| architectures | Array | ["ChatGLMModel"] | 模型架构 | 字符串数组 |

#### 2.1.3 配置验证规则
```java
public class ModelConfigValidator {
    
    public ValidationResult validateModelConfig(Map<String, Object> config) {
        ValidationResult result = new ValidationResult();
        
        // 检查必需字段
        String[] requiredFields = {
            "model_name", "model_type", "vocab_size", "hidden_size",
            "num_attention_heads", "num_hidden_layers", "intermediate_size"
        };
        
        for (String field : requiredFields) {
            if (!config.containsKey(field)) {
                result.addError("Missing required field: " + field);
            }
        }
        
        // 检查数值范围
        if (config.containsKey("vocab_size")) {
            int vocabSize = (Integer) config.get("vocab_size");
            if (vocabSize < 1000 || vocabSize > 100000) {
                result.addError("vocab_size must be between 1000 and 100000");
            }
        }
        
        if (config.containsKey("hidden_size")) {
            int hiddenSize = (Integer) config.get("hidden_size");
            if (hiddenSize < 512 || hiddenSize > 8192) {
                result.addError("hidden_size must be between 512 and 8192");
            }
        }
        
        if (config.containsKey("dropout")) {
            double dropout = (Double) config.get("dropout");
            if (dropout < 0.0 || dropout > 0.5) {
                result.addError("dropout must be between 0.0 and 0.5");
            }
        }
        
        return result;
    }
}
```

### 2.2 training-config.json（训练参数配置）

#### 2.2.1 配置结构设计
```json
{
  "training_config": {
    "model_name": "chatglm3-6b",
    "training_type": "multi_task_learning",
    "base_config": {
      "learning_rate": 2e-5,
      "batch_size": 8,
      "max_epochs": 10,
      "warmup_steps": 100,
      "weight_decay": 0.01,
      "gradient_accumulation_steps": 4,
      "max_grad_norm": 1.0,
      "lr_scheduler_type": "cosine",
      "save_steps": 500,
      "eval_steps": 500,
      "logging_steps": 100,
      "save_total_limit": 3,
      "load_best_model_at_end": true,
      "metric_for_best_model": "eval_loss",
      "greater_is_better": false
    },
    "multi_task_config": {
      "tasks": [
        {
          "name": "article_classification",
          "weight": 0.4,
          "target_loss": 0.05,
          "metrics": ["accuracy", "precision", "recall", "f1"]
        },
        {
          "name": "defect_detection",
          "weight": 0.3,
          "target_loss": 0.1,
          "metrics": ["accuracy", "precision", "recall", "f1"]
        },
        {
          "name": "penalty_prediction",
          "weight": 0.2,
          "target_loss": 0.15,
          "metrics": ["mse", "mae", "r2"]
        },
        {
          "name": "procedure_classification",
          "weight": 0.1,
          "target_loss": 0.1,
          "metrics": ["accuracy", "precision", "recall", "f1"]
        }
      ],
      "loss_aggregation": "weighted_sum",
      "early_stopping_patience": 5,
      "min_improvement": 0.05
    },
    "data_config": {
      "train_file": "/data/training_samples.json",
      "validation_file": "/data/validation_samples.json",
      "test_file": "/data/test_samples.json",
      "text_column": "text",
      "label_columns": {
        "article_label": "article_classification",
        "defect_label": "defect_detection",
        "penalty_amount": "penalty_prediction",
        "procedure_type": "procedure_classification"
      },
      "data_processing": {
        "text_cleaning": true,
        "normalization": true,
        "augmentation": false
      }
    },
    "hardware_config": {
      "device": "cuda",
      "num_gpus": 1,
      "mixed_precision": "fp16",
      "gradient_checkpointing": true,
      "dataloader_num_workers": 4
    }
  }
}
```

#### 2.2.2 配置参数详细说明

**基础训练配置（base_config）**

| 配置项 | 数据类型 | 默认值 | 说明 | 取值范围 |
|--------|----------|--------|------|----------|
| learning_rate | Float | 2e-5 | 学习率 | 1e-6-1e-3 |
| batch_size | Integer | 8 | 批次大小 | 1-64 |
| max_epochs | Integer | 10 | 最大训练轮次 | 1-100 |
| warmup_steps | Integer | 100 | 预热步数 | 0-1000 |
| weight_decay | Float | 0.01 | 权重衰减 | 0.0-0.1 |
| gradient_accumulation_steps | Integer | 4 | 梯度累积步数 | 1-16 |
| max_grad_norm | Float | 1.0 | 最大梯度范数 | 0.1-10.0 |
| lr_scheduler_type | String | "cosine" | 学习率调度器类型 | cosine, linear, step |
| save_steps | Integer | 500 | 保存步数 | 100-5000 |
| eval_steps | Integer | 500 | 评估步数 | 100-5000 |
| logging_steps | Integer | 100 | 日志步数 | 10-1000 |
| save_total_limit | Integer | 3 | 保存模型数量限制 | 1-10 |
| load_best_model_at_end | Boolean | true | 是否在结束时加载最佳模型 | true, false |
| metric_for_best_model | String | "eval_loss" | 最佳模型评估指标 | eval_loss, accuracy, f1 |
| greater_is_better | Boolean | false | 指标是否越大越好 | true, false |

**多任务学习配置（multi_task_config）**

| 配置项 | 数据类型 | 默认值 | 说明 | 取值范围 |
|--------|----------|--------|------|----------|
| tasks | Array | [] | 任务配置列表 | 任务配置对象数组 |
| loss_aggregation | String | "weighted_sum" | 损失聚合方式 | weighted_sum, average |
| early_stopping_patience | Integer | 5 | 早停耐心值 | 1-20 |
| min_improvement | Float | 0.05 | 最小改进阈值 | 0.01-0.1 |

**任务配置（tasks）**

| 配置项 | 数据类型 | 默认值 | 说明 | 取值范围 |
|--------|----------|--------|------|----------|
| name | String | "" | 任务名称 | 字符串 |
| weight | Float | 0.0 | 任务权重 | 0.0-1.0 |
| target_loss | Float | 0.0 | 目标损失 | 0.0-1.0 |
| metrics | Array | [] | 评估指标 | 字符串数组 |

#### 2.2.3 配置验证规则
```java
public class TrainingConfigValidator {
    
    public ValidationResult validateTrainingConfig(Map<String, Object> config) {
        ValidationResult result = new ValidationResult();
        
        // 检查基础配置
        Map<String, Object> baseConfig = (Map<String, Object>) config.get("base_config");
        if (baseConfig != null) {
            validateBaseConfig(baseConfig, result);
        }
        
        // 检查多任务配置
        Map<String, Object> multiTaskConfig = (Map<String, Object>) config.get("multi_task_config");
        if (multiTaskConfig != null) {
            validateMultiTaskConfig(multiTaskConfig, result);
        }
        
        // 检查数据配置
        Map<String, Object> dataConfig = (Map<String, Object>) config.get("data_config");
        if (dataConfig != null) {
            validateDataConfig(dataConfig, result);
        }
        
        return result;
    }
    
    private void validateBaseConfig(Map<String, Object> baseConfig, ValidationResult result) {
        // 检查学习率
        if (baseConfig.containsKey("learning_rate")) {
            double lr = (Double) baseConfig.get("learning_rate");
            if (lr < 1e-6 || lr > 1e-3) {
                result.addError("learning_rate must be between 1e-6 and 1e-3");
            }
        }
        
        // 检查批次大小
        if (baseConfig.containsKey("batch_size")) {
            int batchSize = (Integer) baseConfig.get("batch_size");
            if (batchSize < 1 || batchSize > 64) {
                result.addError("batch_size must be between 1 and 64");
            }
        }
        
        // 检查最大轮次
        if (baseConfig.containsKey("max_epochs")) {
            int maxEpochs = (Integer) baseConfig.get("max_epochs");
            if (maxEpochs < 1 || maxEpochs > 100) {
                result.addError("max_epochs must be between 1 and 100");
            }
        }
    }
    
    private void validateMultiTaskConfig(Map<String, Object> multiTaskConfig, ValidationResult result) {
        // 检查任务配置
        List<Map<String, Object>> tasks = (List<Map<String, Object>>) multiTaskConfig.get("tasks");
        if (tasks != null) {
            double totalWeight = 0.0;
            for (Map<String, Object> task : tasks) {
                if (task.containsKey("weight")) {
                    totalWeight += (Double) task.get("weight");
                }
            }
            
            if (Math.abs(totalWeight - 1.0) > 0.01) {
                result.addError("Sum of task weights must be approximately 1.0");
            }
        }
    }
}
```

### 2.3 evaluation-config.json（评估参数配置）

#### 2.3.1 配置结构设计
```json
{
  "evaluation_config": {
    "model_name": "chatglm3-6b",
    "evaluation_type": "comprehensive",
    "base_config": {
      "batch_size": 16,
      "max_length": 1024,
      "num_beams": 1,
      "do_sample": false,
      "temperature": 1.0,
      "top_p": 1.0,
      "top_k": 50,
      "repetition_penalty": 1.0
    },
    "metrics_config": {
      "classification_metrics": {
        "accuracy": true,
        "precision": true,
        "recall": true,
        "f1_score": true,
        "confusion_matrix": true,
        "classification_report": true
      },
      "regression_metrics": {
        "mse": true,
        "mae": true,
        "r2_score": true,
        "explained_variance": true
      },
      "custom_metrics": {
        "business_accuracy": true,
        "legal_compliance_score": true
      }
    },
    "evaluation_datasets": [
      {
        "name": "test_set",
        "file_path": "/data/test_samples.json",
        "description": "标准测试集"
      },
      {
        "name": "validation_set",
        "file_path": "/data/validation_samples.json",
        "description": "验证集"
      },
      {
        "name": "edge_cases",
        "file_path": "/data/edge_cases.json",
        "description": "边界情况测试集"
      }
    ],
    "output_config": {
      "save_predictions": true,
      "save_metrics": true,
      "generate_report": true,
      "output_dir": "/results/evaluation"
    }
  }
}
```

#### 2.3.2 配置参数详细说明

**基础评估配置（base_config）**

| 配置项 | 数据类型 | 默认值 | 说明 | 取值范围 |
|--------|----------|--------|------|----------|
| batch_size | Integer | 16 | 批次大小 | 1-64 |
| max_length | Integer | 1024 | 最大长度 | 512-2048 |
| num_beams | Integer | 1 | 束搜索数量 | 1-10 |
| do_sample | Boolean | false | 是否采样 | true, false |
| temperature | Float | 1.0 | 温度参数 | 0.1-2.0 |
| top_p | Float | 1.0 | Top-p采样参数 | 0.1-1.0 |
| top_k | Integer | 50 | Top-k采样参数 | 1-100 |
| repetition_penalty | Float | 1.0 | 重复惩罚参数 | 0.8-1.2 |

**评估指标配置（metrics_config）**

| 配置项 | 数据类型 | 默认值 | 说明 | 取值范围 |
|--------|----------|--------|------|----------|
| classification_metrics | Object | {} | 分类指标配置 | 指标配置对象 |
| regression_metrics | Object | {} | 回归指标配置 | 指标配置对象 |
| custom_metrics | Object | {} | 自定义指标配置 | 指标配置对象 |

## 3. 配置管理实现

### 3.1 配置管理器设计

#### 3.1.1 ModelConfigManager.java
```java
package com.ecoenvironment.review.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;
import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 模型配置管理器
 * 负责加载、验证、更新和管理模型相关配置
 */
@Component
public class ModelConfigManager {
    
    private final ObjectMapper objectMapper;
    private final String configBasePath;
    
    // 配置缓存
    private final ConcurrentHashMap<String, Map<String, Object>> configCache;
    private final ConcurrentHashMap<String, Long> configLastModified;
    
    public ModelConfigManager(@Value("${app.config.base-path:/config}") String configBasePath) {
        this.objectMapper = new ObjectMapper();
        this.configBasePath = configBasePath;
        this.configCache = new ConcurrentHashMap<>();
        this.configLastModified = new ConcurrentHashMap<>();
        
        // 初始化时加载所有配置
        loadAllConfigs();
    }
    
    /**
     * 加载所有配置文件
     */
    private void loadAllConfigs() {
        try {
            loadConfig("model-config.json", "model");
            loadConfig("training-config.json", "training");
            loadConfig("evaluation-config.json", "evaluation");
        } catch (Exception e) {
            throw new RuntimeException("Failed to load initial configs", e);
        }
    }
    
    /**
     * 加载指定配置文件
     */
    private void loadConfig(String fileName, String configType) throws IOException {
        File configFile = new File(configBasePath + "/" + fileName);
        if (configFile.exists()) {
            Map<String, Object> config = objectMapper.readValue(configFile, Map.class);
            configCache.put(configType, config);
            configLastModified.put(configType, configFile.lastModified());
        }
    }
    
    /**
     * 获取模型配置
     */
    public Map<String, Object> getModelConfig() {
        return getConfig("model");
    }
    
    /**
     * 获取训练配置
     */
    public Map<String, Object> getTrainingConfig() {
        return getConfig("training");
    }
    
    /**
     * 获取评估配置
     */
    public Map<String, Object> getEvaluationConfig() {
        return getConfig("evaluation");
    }
    
    /**
     * 获取指定类型配置
     */
    private Map<String, Object> getConfig(String configType) {
        // 检查是否需要重新加载
        checkAndReloadConfig(configType);
        return configCache.get(configType);
    }
    
    /**
     * 检查并重新加载配置
     */
    private void checkAndReloadConfig(String configType) {
        String fileName = getConfigFileName(configType);
        File configFile = new File(configBasePath + "/" + fileName);
        
        if (configFile.exists()) {
            long lastModified = configFile.lastModified();
            Long cachedLastModified = configLastModified.get(configType);
            
            if (cachedLastModified == null || lastModified > cachedLastModified) {
                try {
                    loadConfig(fileName, configType);
                } catch (IOException e) {
                    throw new RuntimeException("Failed to reload config: " + configType, e);
                }
            }
        }
    }
    
    /**
     * 获取配置文件名
     */
    private String getConfigFileName(String configType) {
        switch (configType) {
            case "model":
                return "model-config.json";
            case "training":
                return "training-config.json";
            case "evaluation":
                return "evaluation-config.json";
            default:
                throw new IllegalArgumentException("Unknown config type: " + configType);
        }
    }
    
    /**
     * 更新配置
     */
    public void updateConfig(String configType, Map<String, Object> newConfig) {
        try {
            String fileName = getConfigFileName(configType);
            File configFile = new File(configBasePath + "/" + fileName);
            
            // 验证配置
            validateConfig(configType, newConfig);
            
            // 写入文件
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(configFile, newConfig);
            
            // 更新缓存
            configCache.put(configType, newConfig);
            configLastModified.put(configType, configFile.lastModified());
            
        } catch (IOException e) {
            throw new RuntimeException("Failed to update config: " + configType, e);
        }
    }
    
    /**
     * 验证配置
     */
    private void validateConfig(String configType, Map<String, Object> config) {
        ConfigValidator validator = new ConfigValidator();
        ValidationResult result;
        
        switch (configType) {
            case "model":
                result = validator.validateModelConfig(config);
                break;
            case "training":
                result = validator.validateTrainingConfig(config);
                break;
            case "evaluation":
                result = validator.validateEvaluationConfig(config);
                break;
            default:
                throw new IllegalArgumentException("Unknown config type: " + configType);
        }
        
        if (!result.isValid()) {
            throw new IllegalArgumentException("Invalid config: " + String.join(", ", result.getErrors()));
        }
    }
}
```

#### 3.1.2 配置验证器设计
```java
package com.ecoenvironment.review.config;

import org.springframework.stereotype.Component;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.*;

/**
 * 配置验证器
 * 负责验证各种配置的有效性和一致性
 */
@Component
public class ConfigValidator {
    
    private final Validator validator;
    
    public ConfigValidator(Validator validator) {
        this.validator = validator;
    }
    
    /**
     * 验证模型配置
     */
    public ValidationResult validateModelConfig(Map<String, Object> config) {
        ValidationResult result = new ValidationResult();
        
        // 检查必需字段
        String[] requiredFields = {
            "model_name", "model_type", "vocab_size", "hidden_size",
            "num_attention_heads", "num_hidden_layers", "intermediate_size"
        };
        
        for (String field : requiredFields) {
            if (!config.containsKey(field)) {
                result.addError("Missing required field: " + field);
            }
        }
        
        // 检查数值范围
        validateNumericRange(config, "vocab_size", 1000, 100000, result);
        validateNumericRange(config, "hidden_size", 512, 8192, result);
        validateNumericRange(config, "num_attention_heads", 8, 64, result);
        validateNumericRange(config, "num_hidden_layers", 6, 48, result);
        validateNumericRange(config, "dropout", 0.0, 0.5, result);
        
        // 检查字符串字段
        validateStringField(config, "model_name", result);
        validateStringField(config, "model_type", result);
        
        return result;
    }
    
    /**
     * 验证训练配置
     */
    public ValidationResult validateTrainingConfig(Map<String, Object> config) {
        ValidationResult result = new ValidationResult();
        
        // 验证基础配置
        if (config.containsKey("base_config")) {
            Map<String, Object> baseConfig = (Map<String, Object>) config.get("base_config");
            validateBaseConfig(baseConfig, result);
        }
        
        // 验证多任务配置
        if (config.containsKey("multi_task_config")) {
            Map<String, Object> multiTaskConfig = (Map<String, Object>) config.get("multi_task_config");
            validateMultiTaskConfig(multiTaskConfig, result);
        }
        
        // 验证数据配置
        if (config.containsKey("data_config")) {
            Map<String, Object> dataConfig = (Map<String, Object>) config.get("data_config");
            validateDataConfig(dataConfig, result);
        }
        
        return result;
    }
    
    /**
     * 验证评估配置
     */
    public ValidationResult validateEvaluationConfig(Map<String, Object> config) {
        ValidationResult result = new ValidationResult();
        
        // 验证基础配置
        if (config.containsKey("base_config")) {
            Map<String, Object> baseConfig = (Map<String, Object>) config.get("base_config");
            validateEvaluationBaseConfig(baseConfig, result);
        }
        
        // 验证指标配置
        if (config.containsKey("metrics_config")) {
            Map<String, Object> metricsConfig = (Map<String, Object>) config.get("metrics_config");
            validateMetricsConfig(metricsConfig, result);
        }
        
        return result;
    }
    
    /**
     * 验证数值范围
     */
    private void validateNumericRange(Map<String, Object> config, String field, 
                                    double min, double max, ValidationResult result) {
        if (config.containsKey(field)) {
            Object value = config.get(field);
            if (value instanceof Number) {
                double numValue = ((Number) value).doubleValue();
                if (numValue < min || numValue > max) {
                    result.addError(field + " must be between " + min + " and " + max);
                }
            } else {
                result.addError(field + " must be a number");
            }
        }
    }
    
    /**
     * 验证字符串字段
     */
    private void validateStringField(Map<String, Object> config, String field, ValidationResult result) {
        if (config.containsKey(field)) {
            Object value = config.get(field);
            if (!(value instanceof String) || ((String) value).trim().isEmpty()) {
                result.addError(field + " must be a non-empty string");
            }
        }
    }
    
    /**
     * 验证基础训练配置
     */
    private void validateBaseConfig(Map<String, Object> baseConfig, ValidationResult result) {
        validateNumericRange(baseConfig, "learning_rate", 1e-6, 1e-3, result);
        validateNumericRange(baseConfig, "batch_size", 1, 64, result);
        validateNumericRange(baseConfig, "max_epochs", 1, 100, result);
        validateNumericRange(baseConfig, "weight_decay", 0.0, 0.1, result);
    }
    
    /**
     * 验证多任务配置
     */
    private void validateMultiTaskConfig(Map<String, Object> multiTaskConfig, ValidationResult result) {
        if (multiTaskConfig.containsKey("tasks")) {
            List<Map<String, Object>> tasks = (List<Map<String, Object>>) multiTaskConfig.get("tasks");
            if (tasks != null && !tasks.isEmpty()) {
                double totalWeight = 0.0;
                for (Map<String, Object> task : tasks) {
                    if (task.containsKey("weight")) {
                        totalWeight += (Double) task.get("weight");
                    }
                }
                
                if (Math.abs(totalWeight - 1.0) > 0.01) {
                    result.addError("Sum of task weights must be approximately 1.0");
                }
            }
        }
    }
    
    /**
     * 验证数据配置
     */
    private void validateDataConfig(Map<String, Object> dataConfig, ValidationResult result) {
        String[] requiredFiles = {"train_file", "validation_file"};
        for (String fileField : requiredFiles) {
            if (!dataConfig.containsKey(fileField)) {
                result.addError("Missing required data file: " + fileField);
            }
        }
    }
    
    /**
     * 验证评估基础配置
     */
    private void validateEvaluationBaseConfig(Map<String, Object> baseConfig, ValidationResult result) {
        validateNumericRange(baseConfig, "batch_size", 1, 64, result);
        validateNumericRange(baseConfig, "max_length", 512, 2048, result);
        validateNumericRange(baseConfig, "temperature", 0.1, 2.0, result);
    }
    
    /**
     * 验证指标配置
     */
    private void validateMetricsConfig(Map<String, Object> metricsConfig, ValidationResult result) {
        // 检查是否至少配置了一种指标类型
        if (!metricsConfig.containsKey("classification_metrics") && 
            !metricsConfig.containsKey("regression_metrics") && 
            !metricsConfig.containsKey("custom_metrics")) {
            result.addError("At least one metric type must be configured");
        }
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private boolean valid = true;
        private List<String> errors = new ArrayList<>();
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public List<String> getErrors() { return errors; }
        public void addError(String error) { 
            this.errors.add(error);
            this.valid = false;
        }
    }
}
```

## 4. 配置热更新机制

### 4.1 配置监听器设计
```java
package com.ecoenvironment.review.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import java.io.File;
import java.nio.file.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 配置热更新服务
 * 监控配置文件变化并自动重新加载
 */
@Service
public class ConfigReloadService {
    
    @Autowired
    private ModelConfigManager configManager;
    
    private final ConcurrentHashMap<String, Long> lastModifiedTimes;
    private final String configBasePath;
    
    public ConfigReloadService(@Value("${app.config.base-path:/config}") String configBasePath) {
        this.configBasePath = configBasePath;
        this.lastModifiedTimes = new ConcurrentHashMap<>();
        
        // 初始化最后修改时间
        initializeLastModifiedTimes();
    }
    
    /**
     * 初始化最后修改时间
     */
    private void initializeLastModifiedTimes() {
        String[] configFiles = {"model-config.json", "training-config.json", "evaluation-config.json"};
        for (String fileName : configFiles) {
            File configFile = new File(configBasePath + "/" + fileName);
            if (configFile.exists()) {
                lastModifiedTimes.put(fileName, configFile.lastModified());
            }
        }
    }
    
    /**
     * 定时检查配置文件更新
     */
    @Scheduled(fixedRate = 30000) // 每30秒检查一次
    public void checkConfigUpdates() {
        String[] configFiles = {"model-config.json", "training-config.json", "evaluation-config.json"};
        
        for (String fileName : configFiles) {
            checkConfigUpdate(fileName);
        }
    }
    
    /**
     * 检查单个配置文件更新
     */
    private void checkConfigUpdate(String fileName) {
        File configFile = new File(configBasePath + "/" + fileName);
        if (configFile.exists()) {
            try {
                long currentModifiedTime = Files.getLastModifiedTime(configFile.toPath()).toMillis();
                Long lastModifiedTime = lastModifiedTimes.get(fileName);
                
                if (lastModifiedTime == null || currentModifiedTime > lastModifiedTime) {
                    // 配置文件已更新，重新加载
                    String configType = getConfigTypeFromFileName(fileName);
                    configManager.reloadConfig(configType);
                    lastModifiedTimes.put(fileName, currentModifiedTime);
                    
                    System.out.println("Config reloaded: " + fileName + " at " + 
                                     new java.util.Date(currentModifiedTime));
                }
            } catch (Exception e) {
                System.err.println("Failed to check config update: " + fileName + " - " + e.getMessage());
            }
        }
    }
    
    /**
     * 根据文件名获取配置类型
     */
    private String getConfigTypeFromFileName(String fileName) {
        switch (fileName) {
            case "model-config.json":
                return "model";
            case "training-config.json":
                return "training";
            case "evaluation-config.json":
                return "evaluation";
            default:
                throw new IllegalArgumentException("Unknown config file: " + fileName);
        }
    }
}
```

## 5. 配置使用示例

### 5.1 在训练器中使用配置
```java
@Service
public class ReviewCaseTrainer {
    
    @Autowired
    private ModelConfigManager configManager;
    
    @Autowired
    private TrainingConfig trainingConfig;
    
    public void train() {
        // 获取训练配置
        Map<String, Object> trainingConfigMap = configManager.getTrainingConfig();
        Map<String, Object> baseConfig = (Map<String, Object>) trainingConfigMap.get("base_config");
        Map<String, Object> multiTaskConfig = (Map<String, Object>) trainingConfigMap.get("multi_task_config");
        
        // 使用配置参数
        double learningRate = (Double) baseConfig.get("learning_rate");
        int batchSize = (Integer) baseConfig.get("batch_size");
        int maxEpochs = (Integer) baseConfig.get("max_epochs");
        
        // 获取多任务配置
        List<Map<String, Object>> tasks = (List<Map<String, Object>>) multiTaskConfig.get("tasks");
        for (Map<String, Object> task : tasks) {
            String taskName = (String) task.get("name");
            double weight = (Double) task.get("weight");
            double targetLoss = (Double) task.get("target_loss");
            
            System.out.println("Task: " + taskName + ", Weight: " + weight + ", Target Loss: " + targetLoss);
        }
        
        // 开始训练
        startTraining(learningRate, batchSize, maxEpochs, tasks);
    }
    
    private void startTraining(double learningRate, int batchSize, int maxEpochs, 
                             List<Map<String, Object>> tasks) {
        // 训练逻辑实现
        System.out.println("Starting training with learning rate: " + learningRate + 
                          ", batch size: " + batchSize + ", max epochs: " + maxEpochs);
    }
}
```

### 5.2 在评估器中使用配置
```java
@Service
public class ModelEvaluator {
    
    @Autowired
    private ModelConfigManager configManager;
    
    public void evaluate() {
        // 获取评估配置
        Map<String, Object> evaluationConfigMap = configManager.getEvaluationConfig();
        Map<String, Object> baseConfig = (Map<String, Object>) evaluationConfigMap.get("base_config");
        Map<String, Object> metricsConfig = (Map<String, Object>) evaluationConfigMap.get("metrics_config");
        
        // 使用配置参数
        int batchSize = (Integer) baseConfig.get("batch_size");
        int maxLength = (Integer) baseConfig.get("max_length");
        double temperature = (Double) baseConfig.get("temperature");
        
        // 获取指标配置
        Map<String, Object> classificationMetrics = (Map<String, Object>) metricsConfig.get("classification_metrics");
        Map<String, Object> regressionMetrics = (Map<String, Object>) metricsConfig.get("regression_metrics");
        
        // 开始评估
        startEvaluation(batchSize, maxLength, temperature, classificationMetrics, regressionMetrics);
    }
    
    private void startEvaluation(int batchSize, int maxLength, double temperature,
                               Map<String, Object> classificationMetrics,
                               Map<String, Object> regressionMetrics) {
        // 评估逻辑实现
        System.out.println("Starting evaluation with batch size: " + batchSize + 
                          ", max length: " + maxLength + ", temperature: " + temperature);
    }
}
```

## 6. 配置管理最佳实践

### 6.1 配置分层管理
1. **环境配置**：开发、测试、生产环境
2. **功能配置**：模型、训练、评估配置
3. **业务配置**：业务规则、阈值配置

### 6.2 配置安全
1. **敏感信息加密**：API密钥、数据库密码等敏感信息加密存储
2. **访问控制**：基于角色的配置文件访问控制
3. **审计日志**：配置变更的完整审计日志
4. **安全扫描**：定期进行配置安全扫描

### 6.3 配置版本管理
1. **Git管理**：使用Git管理配置文件版本
2. **配置变更记录**：详细的配置变更记录和说明
3. **配置回滚机制**：支持配置快速回滚
4. **配置备份**：定期备份重要配置

### 6.4 配置监控
1. **配置健康检查**：定期检查配置的有效性
2. **配置性能监控**：监控配置加载和使用的性能
3. **配置告警**：配置异常时的告警机制
4. **配置报告**：定期生成配置使用报告

## 7. 总结

本详细报告提供了生态环境行政处罚案卷评查系统的完整模型配置设计方案。通过合理的配置结构设计、验证机制、热更新机制和最佳实践，系统能够灵活地管理各种模型配置，支持多环境部署和配置切换。

设计遵循了配置管理的最佳实践，确保了配置的安全性、一致性和可维护性，同时考虑了系统的可扩展性和性能要求。通过实施本设计方案，系统将能够高效地支持模型训练、评估和推理等核心功能，为生态环境行政处罚案卷评查提供强有力的技术支撑。 