一种生态环境行政处罚案卷评查方法及系统

技术领域
本发明属于执法人工智能技术领域，具体涉及一种生态环境行政处罚案卷评查方法及系统。

背景技术
生态环境行政处罚案卷的评查工作对于规范执法行为、提升执法质量、推进依法行政具有重要意义。传统评查方法主要依赖具有丰富经验的执法专家及律师进行人工评查，存在技术门槛高、评查尺度不一、效率低下等问题，亟需通过人工智能技术进行辅助评查，提升生态环境行政处罚案卷评查效率和质量。
在收集案卷的过程中，全国各地区案卷数量不均，抽取案卷时难以实现均匀分布；案卷种类繁杂，涵盖了行政处罚、配套案卷、不予处罚等多种类型，需设定一个恰当的比例来抽取案卷；最后，在分配案卷时，地方专家和司法专家的参与人数比例存在差异，但需遵守每两人一组、同城专家回避以及平均分配的原则。
在案卷评查方面则考虑到生态环境行政处罚案卷评查工作量大并且对评查人员要求很高的评查经验。生态环境行政处罚案卷平均为200余页，传统人工评审下平均需要4个小时左右。同时全国生态环境行政处罚全国年办理量为约为10万件，如全部按照国家标准进行规范化办理和评查将耗费巨大的人力和财力，并且未必能够达到预期效果。
随着生态环境行政处罚案件数量的增加，传统方法已难以满足高效评查的需求。近年来，大模型技术取得了显著进展，如OpenAI的GPT系列、DeepSeek的技术突破等，展现了强大的文本分析、知识问答和信息抽取能力，为解决生态环境行政处罚案卷评查中的问题提供了新的技术路径。
因此，提供一种基于AI大模型及OCR技术的生态环境行政处罚案卷评审系统，对于解决上述问题至关重要。

发明内容
本发明的目的是提供一种基于AI大模型及OCR技术的生态环境行政处罚案卷评审系统，利用AI大模型技术进行文本学习归纳，并利用建立的法律法规数据库、生态环境处罚案卷评审规则库对案卷信息进行自动评查，并进行分数统计，彻底解决传统行政处罚案卷评查的成本高、效率低、门槛高的问题。
为了实现上述目的，本发明采用的技术方案如下：
第一方面，本发明提供一种生态环境行政处罚案卷评查系统，包括案卷库创建模块、案卷智能抽取模块、OCR识别模块、大模型评查模块；
所述案卷库创建模块包括数据源对接子模块、数据清洗与标准化子模块、分布式存储子模块；
所述数据源对接子模块接入行政处罚系统关系型数据库、卷宗文件库，并以固定时长同步更新获取案卷数据；所获取案卷数据输入数据清洗与标准化子模块，清洗与标准化子模块识别案卷基本信息数据字段、页数并校验，当识别结果中不符合设定的情况则剔除，符合要求的识别结果存储至分布式存储子模块；分布式存储子模块存储符合要求的案卷基本信息数据字段以及指向案卷源文件的路径信息，形成标准化案卷目录库；
所述案卷智能抽取模块包括案卷抽取规则库子模块、案卷抽取信息存储子模块：
所述案卷抽取规则库子模块中设定因素有可编辑的案卷抽取规则，所述因素包括案卷抽取时间、数量、所属区域、案卷类型；所述案卷抽取信息存储子模块根据所选案卷抽取规则，从标准化案卷目录库中随机抽取案卷并形成案卷抽取目录，所述案卷抽取目录信息包括案件编号、案卷名称、案件类型、办理时间、行政区划，关联对应案卷源文件的路径信息，下载存储对应的案卷材料；
所述OCR识别模块对卷智能抽取模块所得的案卷源文件进行识别；所述OCR识别模块包括卷宗分类子模块、OCR识别转换子模块、机器可读文本生成子模块；
所述卷宗分类子模块根据案卷抽取目录信息对案件类型分类；OCR识别转换子模块根据案件类型匹配印刷体、手写体、表格和印章识别策略；机器可读文本生成子模块根据OCR识别转换子模块输出结果，形成含语义标注的机器可读文本；
所述大模型评查模块设有生态环境行政处罚大模型，根据机器可读文本进行实体评查、卷面评查和赋分；所述实体评查包括主体资格核查子模块、法律适用性分析子模块；所述卷面评查包括证据链完整性检查子模块、程序时效性检查子模块、文书规范性检查子模块；
所述主体资格核查子模块中，大模型根据行政机关权限知识图谱判定行政机关主体资格的合法性；法律适用性分析子模块中，大模型计算机器可读文本中的案卷事实描述与法律条款的语义相似度；
所述证据链完整性检查子模块中，大模型依次检查现场检查笔录、监测报告、整改通知书、处罚决定书和送达回证，缺失任一节点则触发证据链断裂预警；
赋分子模块依据实体评查、卷面评查，根据设定权重对生态环境行政处罚结果进行量化赋分；
所述生态环境行政处罚大模型的训练步骤如下：
S1领域自适应预训练：以预训练模型作为原始模型，Prefix Tuning方法注入生态环境领域知识、法律条文、行政处罚裁量基准、司法判例中的程序违规案例，得到初始评查模型；所述生态环境领域知识包括生态环境领域概念及解释；
S2评查专项微调：
S2-1：将含人工复核标记的若干历史评查案例作为训练样本输入初始评查模型，输出预测结果；
S2-2：计算多任务损失函数：
L=a*Larticle+b*Ldefect+c*Lpenalty+d*Lprocedure
L-总损失；
a、b、c、d分别为取值为(0,1)的系数，且a+b+c+d=1；
Larticle为法律条款分类损失值，其取值方式如下：根据模型输出的法律条款与训练样本中案例实际所用法律条款进行对比，根据法律条款的匹配率给出(0,1)分数，模型输出的法律条款与实际所用法律条款匹配率越高，则法律条款分类损失值越低；
Ldefect为缺陷检测损失值，其取值方式如下：模型输出案件的流程存在缺陷的概率值(0,1)，并与训练样本中案例实际的缺陷情况进行比对；若存在缺陷，则Ldefect取值为1-模型输出的概率值；若不存在缺陷，则Ldefect取值为模型输出的概率值；
Lpenalty为处罚金额预测损失值，其取值方式如下：模型输出预测的处罚金额，并与训练样本中案例实际的处罚金额进行比对，输出差值的百分比误差；
Lprocedure为流程损失值，其取值方式如下：模型输出流程类型的判断概率以及流程时间；
当输出为流程类型，则将输出的流程类型概率与训练样本中案例实际的流程类型进行比对，输出值为1-正确预测流程类型的概率；
当输出为流程时间，则取值为模型输出预测的流程时间与案例实际的流程时间差值百分比；
S2-3：当满足以下条件时停止训练：
经过若干次训练后，在验证集上进行一轮验证，分别计算总损失L以及三个核心业务指标Larticle、Ldefect、Lpenalty；所述验证集为非训练样本的历史评查案例；
当总损失L以及Larticle、Ldefect、Lpenalty均低于设定目标值时，停止训练；
当连续N轮验证验证集核心指标提升度均小于5%，则提前停止训练，防止模型出现过拟合现象，节省计算资源；N为设定值，取值为大于2的自然数。
优选地，所述Lpenalty以及Lprocedure流程时间中，采用均方误差（MSE）或者平均绝对百分比误差（MAPE）计算百分比。
优选地，所述a、b、c、d取值分别为0.4、0.3、0.2和0.1。
优选地，所述Larticle、Ldefect、Lpenalty设定目标值分别为0.05、0.1和0.15。
优选地，所述生态环境行政处罚案卷评查系统还包括智能分析模块，所述智能分析模块包括案卷信息分析展示子模块和结果反馈子模块；所述案卷信息分析展示子模块按照行政区、案件类型等多个维度进行统计分析和展示；所述结果反馈模块，统计分数不达设定标准的问题案卷，将问题案卷结果反馈案卷制作单位。
进一步地，所述智能分析模块中，进行智能评查与人工复核结果的关联关系，标记三类差异：严重分歧，评分差30%以上；一般分歧，评分差10~30%；技术性误差，包括识别错误。
优选地，所述清洗与标准化子模块中，所述案卷基本信息数据字段包括实施机关、当事人、案卷类型、案卷编号，当识别结果中存在字段缺失或页数低于设定标准值则剔除，符合要求的识别结果存储至分布式存储子模块
优选地，所述OCR识别转换子模块中还设有预处理步骤，包括以OpenCV去噪、自适应二值化进行图像优化，以PaddleOCR版面检测模型识别文本块、表格、印章区域坐标，以CNN手写区域检测模型分离印刷体与手写体区域。

优选地，所述OCR识别转换子模块的内置工具包括Tesseract、HWR和PaddleOCR。
优选地，所述案件类型包括一般行政处罚、查封扣押、按日计罚、污染犯罪、限产停产、移送公安、不予处罚。
优选地，所述案件抽取过程中，采用差异比对算法：计算两次抽取结果的Jaccard相似度，当相似度大于设定的临界值是，预警重复案件。
优选地，所述原始模型包括ChatGLM3-6B、Qwen、MOSS。
第二方面，本发明提供一种生态环境行政处罚案卷评查方法，以生态环境行政处罚案为输入，使用第一方面所述系统进行评查。
本发明的有益效果：
本发明有如下优势：第一，提高评审效率。系统通过快速处理数据，显著缩短了人工逐项评查的周期，从而提高了整体评查工作效率。第二，统一评审标准，系统通过提供统一标准的算法支持，实现“同案同判”的效果，能够大幅降低错误率，从而减少问题案卷数量。第三，规范打分情况，系统通过展示预评分数，为参与人提供对比参考，缩小卷面分数差距，减少人为干预。第四，帮助查漏补缺。评查系统可以帮助识别案卷中的关键信息，分析潜在的其他问题，防止参与人误判、漏判。
具体实施方式
实施例1
一种生态环境行政处罚案卷评查系统，包括案卷库创建模块、案卷智能抽取模块、OCR识别模块、大模型评查模块、智能分析模块；
所述案卷库创建模块包括数据源对接子模块、数据清洗与标准化子模块、分布式存储子模块；
所述数据源对接子模块接入行政处罚系统关系型数据库、卷宗文件库，并以固定时长同步更新获取案卷数据；输入源为行政处罚系统关系型数据库（MySQL/Oracle），以case_id为主键，包含结构化字段（案卷编号、处罚文号、区域代码、处罚机关、案件来源、案由）；采用CronJob触发ETL流程，通过时间戳字段（last_modified_time）实现增量拉取；
所获取案卷数据输入数据清洗与标准化子模块，清洗与标准化子模块识别案卷基本信息数据字段、页数并校验，当识别结果中不符合设定的情况则剔除，符合要求的识别结果存储至分布式存储子模块；其中，进行完整性校验，若必填字段缺失则丢弃，包括case_id案件ID、decision_date决定下达日期、ecesion_num决定下达文书号；=通过PyPDF2库检测PDF页数，剔除页数＜3的卷宗；
分布式存储子模块存储符合要求的案卷基本信息数据字段以及指向案卷源文件的路径信息，形成标准化案卷目录库；元数据存储以MongoDB分片集群，按region字段分片；卷宗存储为MinIO对象存储，采用case_id+timestamp作为对象命名规范；
所述案卷智能抽取模块包括案卷抽取规则库子模块、案卷抽取信息存储子模块：
所述案卷抽取规则库子模块中设定因素有可编辑的案卷抽取规则，所述因素包括案卷抽取时间、数量、所属区域、案卷类型；所述案卷抽取信息存储子模块根据所选案卷抽取规则，从标准化案卷目录库中随机抽取案卷并形成案卷抽取目录，所述案卷抽取目录信息包括案件编号、案卷名称、案件类型、办理时间、行政区划，关联对应案卷源文件的路径信息，下载存储对应的案卷材料；抽取过程中，采用差异比对算法：计算两次抽取结果的Jaccard相似度，当相似度大于设定的临界值是，预警重复案件。
所述OCR识别模块对卷智能抽取模块所得的案卷源文件进行识别；所述OCR识别模块内置基于案件类型自适应的多引擎调度策略，包括卷宗分类子模块、OCR识别转换子模块、机器可读文本生成子模块；OCR识别模块内置工具包括Tesseract、HWR和PaddleOCR；
所述卷宗分类子模块根据案卷抽取目录信息对案件类型分类；将目标案卷按案件类型进行分类，包括：一般行政处罚、查封扣押、按日计罚、污染犯罪、限产停产、移送公安、不予处罚；
OCR识别转换子模块根据案件类型匹配印刷体、手写体、表格和印章识别策略；OCR识别转换子模块内置预处理步骤，包括以OpenCV去噪、自适应二值化进行图像优化，以PaddleOCR版面检测模型识别文本块、表格、印章区域坐标，以CNN手写区域检测模型分离印刷体与手写体区域；
机器可读文本生成子模块根据OCR识别转换子模块输出结果，形成含语义标注的机器可读文本；

所述大模型评查模块设有生态环境行政处罚大模型，根据机器可读文本进行实体评查、卷面评查和赋分；所述实体评查包括主体资格核查子模块、法律适用性分析子模块；所述卷面评查包括证据链完整性检查子模块、程序时效性检查子模块、文书规范性检查子模块；
所述主体资格核查子模块中，大模型根据行政机关权限知识图谱判定行政机关主体资格的合法性；法律适用性分析子模块中，大模型计算机器可读文本中的案卷事实描述与法律条款的语义相似度；
所述证据链完整性检查子模块中，大模型依次检查现场检查笔录、监测报告、整改通知书、处罚决定书和送达回证，缺失任一节点则触发证据链断裂预警；
赋分子模块依据实体评查、卷面评查，根据设定权重对生态环境行政处罚结果进行量化赋分；
所述智能分析模块包括案卷信息分析展示子模块和结果反馈子模块；所述案卷信息分析展示子模块按照行政区、案件类型等多个维度进行统计分析和展示；所述结果反馈模块，统计分数不达设定标准的问题案卷，将问题案卷结果反馈案卷制作单位；所述智能分析模块中，进行智能评查与人工复核结果的关联关系，标记三类差异：严重分歧，评分差30%以上；一般分歧，评分差10~30%；技术性误差，包括识别错误；
所述生态环境行政处罚大模型的训练步骤如下：
S1领域自适应预训练：以预训练模型作为原始模型，Prefix Tuning方法注入生态环境领域知识、法律条文、行政处罚裁量基准、司法判例中的程序违规案例，得到初始评查模型；
具体地，以50万组训练样本为输入，选用 ChatGLM3-6B 作为基座模型。通过 Prefix Tuning 方法，添加可训练前缀参数矩阵，注入生态环境领域知识，并结合从中国裁判文书网提取的各省市行政处罚裁量基准、司法判例中的程序违规案例；
S2评查专项微调：
S2-1：将含人工复核标记的10万组历史评查案例作为训练样本输入初始评查模型，输出预测结果；
S2-2：计算多任务损失函数：
L=0.4*Larticle+0.3*Ldefect+0.2*Lpenalty+0.1*Lprocedure
L-总损失；
Larticle为法律条款分类损失值，其取值方式如下：根据模型输出的法律条款与训练样本中案例实际所用法律条款进行对比，根据法律条款的匹配率给出(0,1)分数，模型输出的法律条款与实际所用法律条款匹配率越高，则法律条款分类损失值越低；
Ldefect为缺陷检测损失值，其取值方式如下：模型输出案件的流程存在缺陷的概率值(0,1)，并与训练样本中案例实际的缺陷情况进行比对；若存在缺陷，则Ldefect取值为1-模型输出的概率值；若不存在缺陷，则Ldefect取值为模型输出的概率值；
Lpenalty为处罚金额预测损失值，其取值方式如下：模型输出预测的处罚金额，并与训练样本中案例实际的处罚金额进行比对，输出差值的百分比误差；
Lprocedure为流程损失值，其取值方式如下：模型输出流程类型的判断概率以及流程时间；
当输出为流程类型，则将输出的流程类型概率与训练样本中案例实际的流程类型进行比对，输出值为1-正确预测流程类型的概率；
当输出为流程时间，则取值为模型输出预测的流程时间与案例实际的流程时间差值百分比；
S2-3：当满足以下条件时停止训练：
经过若干次训练后，在验证集上进行一轮验证，分别计算总损失L以及三个核心业务指标Larticle、Ldefect、Lpenalty；所述验证集为非训练样本的历史评查案例；
当总损失L以及Larticle、Ldefect、Lpenalty均低于设定目标值时，停止训练；设定目标值分别为0.05、0.1和0.15；
当连续5轮验证验证集核心指标提升度均小于5%，则提前停止训练，防止模型出现过拟合现象，节省计算资源。
本发明能够实现案卷评查的自动化、智能化，减少人工干预，提高评查效率和准确性，降低评查成本，使得更多的生态环境行政处罚案卷能够得到及时、有效的评查，提高生态环境执法审查效率，相较于传统方法能够提升审查速度50%以上。




1、设计理念
2、设计开发流程
3、相关功能代码逻辑流程梳理
4、整体的研发过程
5、研发过程排期表，根据功能点分版本排期、测试排期
6、针对代码的功能说明
7、成品展示：成果数据、（成果截图）


材料、bug、服务器环境、辽宁
